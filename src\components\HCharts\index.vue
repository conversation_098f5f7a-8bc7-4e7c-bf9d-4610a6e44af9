<!--
 * @Author: your name
 * @Date: 2021-10-09 14:52:18
 * @LastEditTime: 2024-07-29 14:39:26
 * @LastEditors: hkh <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \ecological-cultural-tourism\src\components\HCharts\index.vue
-->
<template>
    <div ref="chartdom" class="highcharts-container"></div>
</template>

<script setup>
import { watch, onMounted, defineProps, onBeforeUnmount, ref, defineEmits } from "vue";
import Highcharts from "highcharts/highstock";
import HighchartsMore from "highcharts/highcharts-more";
import HighchartsDrilldown from "highcharts/modules/drilldown";
import Highcharts3D from "highcharts/highcharts-3d";
HighchartsMore(Highcharts);
HighchartsDrilldown(Highcharts);
Highcharts3D(Highcharts);
Highcharts.setOptions({
    chart: {
        backgroundColor: "rgba(255, 255, 255, 0)",
        borderWidth: 0,
        plotBackgroundColor: "rgba(255, 255, 255, 0)",
        plotShadow: false,
        plotBorderWidth: 0,
    },
    credits: {
        enabled: false,
    },
});

let chart = ref(null);
const props = defineProps({
    options: Object,
    styles: String,
});
const timer = ref(null);
const chartdom = ref(null);
onMounted(() => {
    $_init();
    window.addEventListener("resize", resize, false);
});
const emit = defineEmits(["charteEvent"]);

const update3d = () => {
    const each = Highcharts.each;
    const round = Math.round;
    const cos = Math.cos;
    const sin = Math.sin;
    const deg2rad = Math.deg2rad;
    Highcharts.wrap(Highcharts.seriesTypes.pie.prototype, "translate", function(proceed) {
        proceed.apply(this, [].slice.call(arguments, 1));
        // Do not do this if the chart is not 3D
        if (!this.chart.is3d()) {
            return;
        }
        if(!this.chart.options.update3d) {
            //如果需要有高低不同的3D饼图
            return;
        }
        const series = this;
        const chart = series.chart;
        const options = chart.options;
        const seriesOptions = series.options;
        const depth = seriesOptions.depth || 0;
        const options3d = options.chart.options3d;
        const alpha = options3d.alpha;
        const beta = options3d.beta;
        var z = seriesOptions.stacking ? (seriesOptions.stack || 0) * depth : series._i * depth;
        z += depth / 2;
        if (seriesOptions.grouping !== false) {
            z = 0;
        }
        each(series.data, function(point) {
            const shapeArgs = point.shapeArgs;
            var angle;
            point.shapeType = "arc3d";
            var ran = point.options.h || 5;
            shapeArgs.z = z;
            shapeArgs.depth = depth * 0.75 + ran;
            shapeArgs.alpha = alpha;
            shapeArgs.beta = beta;
            shapeArgs.center = series.center;
            shapeArgs.ran = ran;
            angle = (shapeArgs.end + shapeArgs.start) / 2;
            point.slicedTranslation = {
                translateX: round(cos(angle) * seriesOptions.slicedOffset * cos(alpha * deg2rad)),
                translateY: round(sin(angle) * seriesOptions.slicedOffset * cos(alpha * deg2rad)),
            };
        });
    });
    (function(H) {
        H.wrap(Highcharts.SVGRenderer.prototype, "arc3dPath", function(proceed) {
            // Run original proceed method
            const ret = proceed.apply(this, [].slice.call(arguments, 1));
            ret.zTop = (ret.zOut + 0.5) / 100;
            return ret;
        });
    })(Highcharts);
}

const $_init = () => {
    update3d();
    chart.value = new Highcharts.Chart(chartdom.value, props.options);
    window.hhhc = chart.value
    //   chart.value = echarts.init(chartdom.value, props.theme, { devicePixelRatio: 2 });
    emit("charteEvent", chart.value);
};
const $_setOption = () => {
    if (props.options && chart.value) {
        // update3d();
        chart.value.update(props.options, true, true, true);
        // unwarp(chart.value).setOption(props.options, true);
    }
};
const resize = () => {
    // clearTimeout(timer.value);
    // timer.value = setTimeout(() => {
    //     // update3d();
    //     chart.value.redraw();
    // }, 300);
};
watch(
    () => props.options,
    () => {
        $_setOption();
    },
    // 可选项，是否开启深监听
    {
        deep: true,
        immediate: true,
    },
);
// watch(
//     () => chart.value,
//     () => {
//         $_setOption();
//     },
// );

onBeforeUnmount(() => {
    //   unwarp(chart.value)?.dispose();
    chart.value?.destroy();
    chart.value = null;
    window.removeEventListener("resize", resize);
});
// export default {
//     data() {
//         return {
//             chart: null,
//         };
//     },
//     watch: {
//         options: {
//             immediate: true,
//             handler: "$_setOption",
//         },
//         styles: "$_setOption",
//     },
//     mounted() {
//         this.initChart();
//     },
//     methods: {
//         initChart() {
//             console.log(this.$el);
//             this.$el.style.width = this.styles.width + "px";
//             this.$el.style.height = this.styles.height + "px";
//             // this.$el.style.width = (this.styles.width || 800) + "px";
//             // this.$el.style.height = (this.styles.height || 400) + "px";
//             this.chart = new Highcharts.Chart(this.$el, this.options);
//         },
//         $_setOption() {
//             if (!this.chart) return;
//             this.chart.update(this.options, true, true, true);
//         },
//     },
// };
</script>

<style lang="scss">
.highcharts-container {
    width: 100%;
    height: 100%;
}
</style>
