<template>
    <div class="left-menu">
        <div class="left-menu-title">
            <!-- 动态引入图片 -->
            <img :src="menuTitleImg" alt="" />
            <span> 深圳体育中心智慧指挥中心管理后台 </span>
        </div>
        <div
            class="left-menu-item"
            :class="{ active: activeMenu === item.name }"
            @click="toggleMenu(item, index)"
            v-for="(item, index) in menu"
            :key="index"
        >
            <!-- 动态引入图片 -->
            <img :src="getMenuItemImg(item, activeMenu === item.name)" alt="" />
            <div class="left-menu-item-title">{{ item.title }}</div>
            <!-- <div class="left-menu-item-content" v-show="isMenuOpen[index]">
          <div class="left-menu-item-content-item" v-for="(subItem, subIndex) in item.children" :key="subIndex">
          <router-link :to="subItem.path">{{ subItem.title }}</router-link>
          </div>
        </div> -->
        </div>
    </div>
</template>
<script setup>
import { ref, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import menuTitleImg from "@/assets/logo.png";
import yljlImg from "@/assets/yljl.png";
import yjyaImg from "@/assets/yjya.png";
import sbglImg from "@/assets/sbgl.png";
import yljl_activeImg from "@/assets/yljl_active.png";
import yjya_activeImg from "@/assets/yjya_active.png";
import sbgl_activeImg from "@/assets/sbgl_active.png";

const router = useRouter();
const route = useRoute();

const activeMenu = ref("yljl");

const menu = ref([
    {
        title: "演练记录管理",
        name: "yljl",
        img: yljlImg,
        img_active: yljl_activeImg,
    },
    {
        title: "应急预案管理",
        name: "yjya",
        img: yjyaImg,
        img_active: yjya_activeImg,
    },
    {
        title: "应急事件",
        name: "yjsj",
        img: yjyaImg,
        img_active: yjya_activeImg,
    },
    {
        title: "应急组织架构",
        name: "yjzzjg",
        img: yjyaImg,
        img_active: yjya_activeImg,
    },
    {
        title: "应急救援队伍",
        name: "yjjydw",
        img: yjyaImg,
        img_active: yjya_activeImg,
    },
    {
        title: "物资仓库",
        name: "wzck",
        img: sbglImg,
        img_active: sbgl_activeImg,
    },
    {
        title: "大型装备",
        name: "dxzb",
        img: sbglImg,
        img_active: sbgl_activeImg,
    },
    {
        title: "排班表",
        name: "pbb",
        img: yljlImg,
        img_active: yljl_activeImg,
    },
]);

// 根据菜单名称动态获取图片
// const imageImports = import.meta.glob("@/assets/*.png", { eager: true });
// window.ssss = import.meta;
const getMenuItemImg = (name, isActive) => {
    return isActive ? name.img_active : name.img;
    // let path;
    // for (const key in imageImports) {
    //     if (key.includes(isActive ? name + "_active" : name)) {
    //         path = key;
    //         break;
    //     }
    // }
    // console.log(path);

    // return path || "";
};

window.route = route;
watch(
    () => route.name,
    (newVal) => {
        activeMenu.value = newVal;
    },
    {
        deep: true,
        immediate: true,
    },
);

const toggleMenu = (params, index) => {
    router.push({ name: params.name });
};
</script>
<style scoped lang="scss">
.left-menu {
    width: 336px;
    height: 100%;
    color: #000;
    background: #f5f6fc;
    box-shadow: 1px 0px 10px 0px rgba(0, 0, 0, 0.16);
    padding: 26px 16px;
    z-index: 999;
    .left-menu-title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 22px;
        line-height: 32px;
        letter-spacing: 3px;
        img {
            width: 77px;
            height: 67px;
            margin-right: 12px;
        }
    }
    .left-menu-item {
        height: 44px;
        line-height: 44px;
        padding: 0 50px;
        width: 100%;
        color: #969ea7;
        margin: 16px 0;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        img {
            width: 24px;
            height: 24px;
            margin-right: 26px;
        }
        &.active {
            color: #2993fd;
        }
    }
}
</style>
