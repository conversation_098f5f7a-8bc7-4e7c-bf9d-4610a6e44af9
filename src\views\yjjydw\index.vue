<template>
    <div class="right">
        <BaseTitle />
        <div class="body">
            <div class="title">应急救援队伍</div>
            <div class="search-box">
                <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                    <el-form-item label="姓名">
                        <el-input v-model="searchForm.name" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="岗位类型">
                        <el-select v-model="searchForm.position" placeholder="请选择" clearable>
                            <el-option label="应急指挥" value="应急指挥" />
                            <el-option label="搜索救援" value="搜索救援" />
                            <el-option label="医疗救护" value="医疗救护" />
                            <el-option label="通信保障" value="通信保障" />
                            <el-option label="后勤保障" value="后勤保障" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="onReset">重置</el-button>
                        <el-button type="primary" @click="onSubmit">查询</el-button>
                    </el-form-item>
                </el-form>
                <div class="action-buttons">
                    <el-button type="primary" @click="handleImport">
                        <el-icon><Upload /></el-icon>
                        导入
                    </el-button>
                    <el-button type="primary" @click="handleAdd"> 新增 </el-button>
                </div>
            </div>
            <div class="table-box">
                <el-table
                    ref="tableRef"
                    :data="tableData"
                    style="width: 100%"
                    :height="tableHeight"
                    :loading="tableLoading"
                    :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: '600',
                        fontSize: '14px',
                        height: '50px',
                    }"
                    :row-style="{ height: '48px' }"
                    :cell-style="{ fontSize: '13px', color: '#606266' }"
                >
                    <el-table-column type="index" label="序号" width="80" />
                    <el-table-column prop="position" label="岗位类型" width="150" />
                    <el-table-column prop="function" label="职能分工" />
                    <el-table-column prop="department" label="所属部门" width="200" />
                    <el-table-column prop="name" label="姓名" width="120" />
                    <el-table-column prop="contact" label="联系方式" width="150" />
                    <el-table-column label="操作" width="150">
                        <template #default="scope">
                            <el-button type="text" @click="handleView(scope.row)">详情</el-button>
                            <el-popconfirm title="确认删除?" @confirm="handleDelete(scope.row)">
                                <template #reference>
                                    <el-button type="text" style="color: #f56c6c">删除</el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50]"
                    :disabled="disabled"
                    :background="background"
                    layout=" prev, pager, next, total, sizes, jumper"
                    :total="total"
                    class="page-box"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>

        <!-- 新增/编辑弹框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogMode === 'add' ? '新增队员' : '编辑队员'"
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form :model="formData" label-width="120px" :rules="rules" ref="formRef">
                <el-form-item label="岗位类型" prop="position">
                    <el-select
                        v-model="formData.position"
                        placeholder="请选择岗位类型"
                        style="width: 100%"
                    >
                        <el-option label="应急指挥" value="应急指挥" />
                        <el-option label="搜索救援" value="搜索救援" />
                        <el-option label="医疗救护" value="医疗救护" />
                        <el-option label="通信保障" value="通信保障" />
                        <el-option label="后勤保障" value="后勤保障" />
                    </el-select>
                </el-form-item>
                <el-form-item label="职能分工" prop="function">
                    <el-input v-model="formData.function" placeholder="请输入职能分工" />
                </el-form-item>
                <el-form-item label="所属部门" prop="department">
                    <el-input v-model="formData.department" placeholder="请输入所属部门" />
                </el-form-item>
                <el-form-item label="姓名" prop="name">
                    <el-input v-model="formData.name" placeholder="请输入姓名" />
                </el-form-item>
                <el-form-item label="联系方式" prop="contact">
                    <el-input v-model="formData.contact" placeholder="请输入联系方式" />
                </el-form-item>
                <el-form-item label="备注">
                    <el-input
                        v-model="formData.remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入备注"
                    />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">确定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 详情弹框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="队员详情"
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form :model="detailData" label-width="120px">
                <el-form-item label="岗位类型">
                    <span class="form-value">{{ detailData.position || "-" }}</span>
                </el-form-item>
                <el-form-item label="职能分工">
                    <span class="form-value">{{ detailData.function || "-" }}</span>
                </el-form-item>
                <el-form-item label="所属部门">
                    <span class="form-value">{{ detailData.department || "-" }}</span>
                </el-form-item>
                <el-form-item label="姓名">
                    <span class="form-value">{{ detailData.name || "-" }}</span>
                </el-form-item>
                <el-form-item label="联系方式">
                    <span class="form-value">{{ detailData.contact || "-" }}</span>
                </el-form-item>
                <el-form-item label="备注">
                    <span class="form-value">{{ detailData.remark || "-" }}</span>
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入弹框 -->
        <el-dialog
            v-model="uploadDialogVisible"
            title="导入队员信息"
            width="500px"
            :close-on-click-modal="false"
        >
            <el-upload
                class="upload-demo"
                action=""
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :file-list="uploadFileList"
                multiple
                drag
            >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">将文件拖拽到此处，或<em>点击上传</em></div>
                <template #tip>
                    <div class="el-upload__tip">支持 Excel 格式文件，单个文件不超过 10MB</div>
                </template>
            </el-upload>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="uploadDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmUpload">确认导入</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import BaseTitle from "@/components/BaseTitle/index.vue";
import { ElMessage } from "element-plus";
import { Upload, UploadFilled } from "@element-plus/icons-vue";

const router = useRouter();

// 表格数据
const tableRef = ref();
const tableData = ref([]);
const tableLoading = ref(false);

// 计算表格高度
const tableHeight = computed(() => {
    return "calc(100vh - 320px)";
});

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const background = ref(false);
const disabled = ref(false);

// 搜索表单
const searchForm = reactive({
    name: "",
    position: "",
});

// 弹框相关
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const uploadDialogVisible = ref(false);
const dialogMode = ref("add"); // add, edit
const formRef = ref();
const uploadFileList = ref([]);

const formData = reactive({
    id: null,
    position: "",
    function: "",
    department: "",
    name: "",
    contact: "",
    remark: "",
});

const detailData = ref({});

// 表单验证规则
const rules = {
    position: [{ required: true, message: "请选择岗位类型", trigger: "change" }],
    function: [{ required: true, message: "请输入职能分工", trigger: "blur" }],
    department: [{ required: true, message: "请输入所属部门", trigger: "blur" }],
    name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
    contact: [
        { required: true, message: "请输入联系方式", trigger: "blur" },
        { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" },
    ],
};

// 初始化数据
const initData = () => {
    tableData.value = [
        {
            id: 1,
            position: "应急指挥",
            function: "总指挥",
            department: "应急指挥部",
            name: "王林",
            contact: "15627463728",
            remark: "负责整体指挥协调",
        },
        {
            id: 2,
            position: "搜索救援",
            function: "救援队长",
            department: "救援一队",
            name: "王林",
            contact: "15627463728",
            remark: "负责现场搜救工作",
        },
    ];
    total.value = tableData.value.length;
};

// 查询
const onSubmit = () => {
    currentPage.value = 1;
    handleSearch();
};

// 重置
const onReset = () => {
    Object.assign(searchForm, {
        name: "",
        position: "",
    });
    onSubmit();
};

// 搜索
const handleSearch = () => {
    tableLoading.value = true;
    setTimeout(() => {
        initData();
        tableLoading.value = false;
    }, 500);
};

// 分页处理
const handleSizeChange = (val) => {
    pageSize.value = val;
    handleSearch();
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
    handleSearch();
};

// 重置表单
const resetForm = () => {
    Object.assign(formData, {
        id: null,
        position: "",
        function: "",
        department: "",
        name: "",
        contact: "",
        remark: "",
    });
};

// 新增
const handleAdd = () => {
    dialogMode.value = "add";
    resetForm();
    dialogVisible.value = true;
};

// 查看详情
const handleView = (row) => {
    detailData.value = { ...row };
    detailDialogVisible.value = true;
};

// 删除
const handleDelete = (row) => {
    ElMessage.success("删除成功");
    handleSearch();
};

// 提交表单
const handleSubmit = () => {
    formRef.value.validate((valid) => {
        if (valid) {
            ElMessage.success("操作成功");
            dialogVisible.value = false;
            handleSearch();
        }
    });
};

// 导入相关
const handleImport = () => {
    uploadDialogVisible.value = true;
};

const beforeUpload = (file) => {
    const isExcel =
        file.type === "application/vnd.ms-excel" ||
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    const isLt10M = file.size / 1024 / 1024 < 10;

    if (!isExcel) {
        ElMessage.error("只能上传 Excel 格式的文件!");
        return false;
    }
    if (!isLt10M) {
        ElMessage.error("上传文件大小不能超过 10MB!");
        return false;
    }
    return true;
};

const handleUploadSuccess = (response, file, fileList) => {
    ElMessage.success("文件上传成功");
    uploadFileList.value = fileList;
};

const handleUploadError = (error, file, fileList) => {
    ElMessage.error("文件上传失败");
};

const confirmUpload = () => {
    if (uploadFileList.value.length === 0) {
        ElMessage.warning("请选择要上传的文件");
        return;
    }

    ElMessage.success("导入成功");
    uploadDialogVisible.value = false;
    uploadFileList.value = [];
    handleSearch();
};

onMounted(() => {
    handleSearch();
});
</script>

<style scoped lang="scss">
.right {
    width: calc(100% - 336px);
}

.body {
    padding: 50px 50px;
    height: calc(100% - 122px);
    display: flex;
    flex-direction: column;

    .title {
        margin-bottom: 50px;
        font-size: 20px;
        font-weight: bold;
    }
}

.search-box {
    height: 124px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 62px;
    background: #ffffff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;

    .demo-form-inline {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-left: 20px;
    }
}

.table-box {
    margin-top: 40px;
    height: calc(100% - 214px);
    padding: 30px 62px;
    background: #fff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;

    .page-box {
        display: flex;
        justify-content: flex-end;
        margin-top: 30px;
    }
}

.form-value {
    color: #303133;
    font-size: 14px;
    line-height: 32px;
}

.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    border-top: 1px solid #ebeef5;
    padding-top: 15px;

    .el-button {
        padding: 12px 32px;
        border-radius: 8px;
        font-weight: 500;
        min-width: 100px;

        &.el-button--primary {
            background: linear-gradient(135deg, #409eff 0%, #367cc7 100%);
            border-color: #409eff;

            &:hover {
                background: linear-gradient(135deg, #66b1ff 0%, #5a8dd1 100%);
            }
        }

        &:not(.el-button--primary) {
            border-color: #dcdfe6;
            color: #606266;

            &:hover {
                border-color: #409eff;
                color: #409eff;
            }
        }
    }
}

.upload-demo {
    width: 100%;

    .el-upload {
        width: 100%;
    }

    .el-upload-dragger {
        width: 100%;
        height: 120px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s;

        &:hover {
            border-color: #2993fd;
            background: #f5f9ff;
        }
    }
}

:deep(.el-table) {
    border: 1px solid #ebeef5;
    border-radius: 4px;
}

:deep(.el-table .el-table__header-wrapper) {
    border-radius: 4px 4px 0 0;
}

:deep(.el-table td, .el-table th) {
    border-bottom: 1px solid #ebeef5;
}

:deep(.el-table th) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table td) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table th:last-child) {
    border-right: none;
}

:deep(.el-table td:last-child) {
    border-right: none;
}

:deep(.el-table th:last-child) {
    border-right: none;
}

:deep(.el-table td:last-child) {
    border-right: none;
}

:deep(.el-dialog) {
    border-radius: 8px;
}

:deep(.el-dialog__header) {
    background: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    padding: 20px 24px;
}

:deep(.el-dialog__title) {
    color: #303133;
    font-weight: 600;
    font-size: 16px;
}

:deep(.el-dialog__body) {
    padding: 24px;
}

// 表单样式优化
.el-form {
    .el-form-item {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .el-form-item__label {
        font-weight: 500;
        color: #606266;
        line-height: 40px;

        &::before {
            color: #f56c6c;
            margin-right: 4px;
        }
    }

    .el-input,
    .el-select,
    .el-textarea {
        .el-input__wrapper {
            border-radius: 8px;
            transition: all 0.3s;

            &:hover {
                box-shadow: 0 0 0 1px #409eff inset;
            }
        }
    }

    .el-textarea .el-textarea__inner {
        border-radius: 8px;
        padding: 12px 16px;
        line-height: 1.5;
        font-size: 14px;
    }
}

:deep(.el-button) {
    border-radius: 8px;
    padding: 12px 16px;
    font-weight: 500;
}

:deep(.el-button--primary) {
    background: linear-gradient(135deg, #409eff 0%, #367cc7 100%);
    border-color: #409eff;

    &:hover {
        background: linear-gradient(135deg, #66b1ff 0%, #5a8dd1 100%);
    }
}

.el-form-item {
    margin-bottom: 0;
}

.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
</style>
