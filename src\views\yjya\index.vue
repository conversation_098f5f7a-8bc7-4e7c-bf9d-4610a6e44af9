<template>
    <div class="right">
        <BaseTitle />
        <div class="body">
            <div class="title">应急预案管理</div>
            <div class="search-box">
                <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                    <el-form-item label="预案名称">
                        <el-input v-model="searchForm.name" placeholder="预案名称" clearable />
                    </el-form-item>
                    <el-form-item label="预案类型">
                        <el-select v-model="searchForm.type" placeholder="请选择" clearable>
                            <el-option label="火灾事故" value="火灾事故" />
                            <el-option label="地震灾害" value="地震灾害" />
                            <el-option label="洪涝灾害" value="洪涝灾害" />
                            <el-option label="突发事件" value="突发事件" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="onReset">重置</el-button>
                        <el-button type="primary" @click="onSubmit">查询</el-button>
                    </el-form-item>
                </el-form>
                <div class="import-btn">
                    <el-button type="primary" @click="handleImport">
                        <el-icon><Upload /></el-icon>
                        导入
                    </el-button>
                </div>
            </div>
            <div class="table-box">
                <el-table
                    ref="multipleTableRef"
                    :data="tableData"
                    row-key="id"
                    style="width: 100%; height: 451px"
                    :loading="tableLoading"
                    :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: '600',
                        fontSize: '14px',
                        height: '50px',
                    }"
                    :row-style="{ height: '48px' }"
                    :cell-style="{ fontSize: '13px', color: '#606266' }"
                >
                    <el-table-column type="index" label="序号" width="80" />
                    <el-table-column property="name" label="预案名称" />
                    <el-table-column property="type" label="预案类型" width="350" />
                    <el-table-column label="操作" width="300">
                        <template #default="scope">
                            <el-button type="text" @click="handleDownload(scope.row)"
                                >下载</el-button
                            >
                            <el-button type="text" @click="handleView(scope.row)">详情</el-button>
                            <el-popconfirm title="确认删除?" @confirm="handleDelete(scope.row)">
                                <template #reference>
                                    <el-button type="text" style="color: #f56c6c">删除</el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50]"
                    :disabled="disabled"
                    :background="background"
                    layout=" prev, pager, next, total, sizes, jumper"
                    :total="total"
                    class="page-box"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>

        <!-- 预案详情弹框 -->
        <el-dialog
            v-model="dialogVisible"
            title="预案详情"
            width="600px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="dialog-content">
                <el-form :model="formData" label-width="100px">
                    <el-form-item label="预案名称">
                        <span class="form-value">{{ formData.name || "-" }}</span>
                    </el-form-item>
                    <el-form-item label="预案类型">
                        <span class="form-value">{{ formData.type || "-" }}</span>
                    </el-form-item>
                    <el-form-item label="预案文件">
                        <div v-if="formData.fileList && formData.fileList.length > 0">
                            <div
                                v-for="(file, index) in formData.fileList"
                                :key="index"
                                class="file-item"
                            >
                                <el-icon><Document /></el-icon>
                                <span class="file-name">{{ file.name }}</span>
                                <el-button type="text" @click="downloadFile(file)">下载</el-button>
                            </div>
                        </div>
                        <span v-else class="form-value">暂无文件</span>
                    </el-form-item>
                </el-form>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 文件上传弹框 -->
        <el-dialog
            v-model="uploadDialogVisible"
            title="上传预案文件"
            width="500px"
            :close-on-click-modal="false"
        >
            <el-upload
                class="upload-demo"
                action=""
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :file-list="uploadFileList"
                multiple
                drag
            >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">将文件拖拽到此处，或<em>点击上传</em></div>
                <template #tip>
                    <div class="el-upload__tip">
                        支持 PDF、DOC、DOCX 等格式文件，单个文件不超过 10MB
                    </div>
                </template>
            </el-upload>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="uploadDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmUpload">确认上传</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, watch, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import BaseTitle from "@/components/BaseTitle/index.vue";
import { YjyaList, YjyaExport, YjyaDel, YjyaUpload, YjyaDownload, YjyaDetail } from "@/apis";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { Document, UploadFilled, Upload } from "@element-plus/icons-vue";

// 注册图标组件
import { ElIcon } from "element-plus";

const router = useRouter();
const route = useRoute();

const multipleTableRef = ref("");
const tableData = ref([]);

const onSubmit = () => {
    currentPage.value = 1;
    pageSize.value = 10;
    handleSearch();
};

const onReset = () => {
    currentPage.value = 1;
    pageSize.value = 10;
    searchForm.name = "";
    searchForm.type = "";
    handleSearch();
};
const tableLoading = ref(false);

const handleSearch = () => {
    tableData.value = [];
    total.value = 0;
    tableLoading.value = true;
    YjyaList({
        pageNum: currentPage.value,
        pageSize: pageSize.value,
        name: searchForm.name,
        type: searchForm.type,
    }).then((res) => {
        tableData.value = res.data;
        total.value = res.total;
        tableLoading.value = false;
    });
};

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const background = ref(false);
const disabled = ref(false);

const handleSizeChange = (val) => {
    pageSize.value = val;
    handleSearch();
};
const handleCurrentChange = (val) => {
    currentPage.value = val;
    handleSearch();
};

const searchForm = reactive({
    name: "",
    type: "",
});

onMounted(() => {
    onSubmit();
});

const handleView = (row) => {
    loadDetail(row.id);
};

// 加载详情数据
const loadDetail = (id) => {
    YjyaDetail({ id }).then((res) => {
        const data = res.data;

        formData.value = {
            id: data.id,
            name: data.name || data.title,
            type: data.type || "火灾事故",
            fileList: data.fileUrl
                ? [
                      {
                          name: data.fileUrl,
                          url: data.fileUrl,
                      },
                  ]
                : [],
        };

        dialogVisible.value = true;
    });
};

const handleExport = (row) => {
    // window.open(YjyaExport({ id: row.id }));
    YjyaExport({
        id: row.id,
    }).then((res) => {
        window.open(res.data);
    });
};

// 文件下载
const downloadFile = (file) => {
    if (file.url) {
        YjyaDownload({ fileName: file.url }).then((res) => {
            // 这里可以处理下载逻辑
            window.open(res.data);
        });
    }
};

// 文件上传相关
const beforeUpload = (file) => {
    const isValidType =
        file.type === "application/pdf" ||
        file.type === "application/msword" ||
        file.type === "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    const isLt10M = file.size / 1024 / 1024 < 10;

    if (!isValidType) {
        ElMessage.error("只能上传 PDF、DOC、DOCX 格式的文件!");
        return false;
    }
    if (!isLt10M) {
        ElMessage.error("上传文件大小不能超过 10MB!");
        return false;
    }
    return true;
};

const handleUploadSuccess = (response, file, fileList) => {
    ElMessage.success("文件上传成功");
    uploadFileList.value = fileList;
};

const handleUploadError = (error, file, fileList) => {
    ElMessage.error("文件上传失败");
};

const confirmUpload = () => {
    if (uploadFileList.value.length === 0) {
        ElMessage.warning("请选择要上传的文件");
        return;
    }

    // 这里处理文件上传逻辑
    const formData = new FormData();
    uploadFileList.value.forEach((file) => {
        formData.append("files", file.raw);
    });

    YjyaUpload(formData)
        .then((res) => {
            ElMessage.success("文件上传成功");
            uploadDialogVisible.value = false;
            uploadFileList.value = [];
            onSubmit(); // 刷新列表
        })
        .catch(() => {
            ElMessage.error("文件上传失败");
        });
};

const handleDelete = (row) => {
    YjyaDel({
        ids: row.id,
    }).then((res) => {
        ElMessage({
            message: "删除成功",
            type: "success",
        });
        onSubmit();
    });
};

// 导入功能
const handleImport = () => {
    uploadDialogVisible.value = true;
};

// 弹框相关
const dialogVisible = ref(false);
const formData = ref({
    id: null,
    name: "",
    type: "",
    fileList: [],
});

// 重置表单数据
const resetFormData = () => {
    formData.value = {
        id: null,
        name: "",
        type: "",
        fileList: [],
    };
};

// 文件上传弹框
const uploadDialogVisible = ref(false);
const uploadFileList = ref([]);

// 下载预案文件
const handleDownload = (row) => {
    YjyaExport({ id: row.id })
        .then((res) => {
            window.open(res.data);
        })
        .catch(() => {
            ElMessage({
                message: "下载失败",
                type: "error",
            });
        });
};
</script>

<style scoped lang="scss">
.right {
    width: calc(100% - 336px);
}
.body {
    padding: 50px 50px;
    height: calc(100% - 122px);
    .title {
        margin-bottom: 50px;
        font-size: 20px;
        font-weight: bold;
    }
}
.search-box {
    height: 84px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 62px;
    background: #ffffff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;

    .demo-form-inline {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .import-btn {
        margin-left: 20px;
    }
}
.table-box {
    margin-top: 40px;
    height: calc(100% - 174px);
    padding: 30px 62px;
    background: #fff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;
    .page-box {
        display: flex;
        justify-content: flex-end;
        margin-top: 30px;
    }
}

:deep(.el-table) {
    border: 1px solid #ebeef5;
    border-radius: 4px;
}

:deep(.el-table .el-table__header-wrapper) {
    border-radius: 4px 4px 0 0;
}

:deep(.el-table td, .el-table th) {
    border-bottom: 1px solid #ebeef5;
}

:deep(.el-table th) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table td) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table th:last-child) {
    border-right: none;
}

:deep(.el-table td:last-child) {
    border-right: none;
}
.el-form-item {
    margin-bottom: 0;
}
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}

/* 弹框样式 */
.dialog-content {
    padding: 20px 0;
}

.dialog-footer {
    text-align: right;
    border-top: 1px solid #ebeef5;
    padding-top: 15px;
}

.form-value {
    color: #303133;
    font-size: 14px;
    line-height: 32px;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }

    .file-name {
        flex: 1;
        color: #333;
    }
}

.upload-demo {
    width: 100%;

    .el-upload {
        width: 100%;
    }

    .el-upload-dragger {
        width: 100%;
        height: 120px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s;

        &:hover {
            border-color: #2993fd;
            background: #f5f9ff;
        }
    }
}

/* 弹框内表单样式优化 */
:deep(.el-dialog) {
    border-radius: 8px;
}

:deep(.el-dialog__header) {
    background: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    padding: 20px 24px;
}

:deep(.el-dialog__title) {
    color: #303133;
    font-weight: 600;
    font-size: 16px;
}

:deep(.el-dialog__body) {
    padding: 24px;
}

:deep(.el-form-item) {
    margin-bottom: 20px;
}

:deep(.el-form-item__label) {
    color: #606266;
    font-weight: 500;
}

:deep(.el-button) {
    border-radius: 4px;
    padding: 8px 16px;
}

:deep(.el-button--primary) {
    background: #409eff;
    border-color: #409eff;

    &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
    }
}
</style>
