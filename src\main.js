/*
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-16 16:13:16
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2025-03-17 13:01:52
 * @FilePath: \chengde-computer-room\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from "vue";
import App from "./App.vue";
import { router } from "./router";
import store from "./store";
import { createPinia } from "pinia";
import "@/public/utils/helper.js";
import Adaption from "@/public/class/Adaption";
import "@/public/styles/index.scss";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/dark/css-vars.css";
import ECharts from "@/components/Echarts";

import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import zhCn from "element-plus/es/locale/lang/zh-cn";

// import Highcharts from 'highcharts/es-modules/Core/Chart/Chart.js';
// import LineSeries from 'highcharts/es-modules/Series/Line/LineSeries.js';
// import GaugeSeries from "highcharts/es-modules/Series/Gauge/GaugeSeries.js";

(window.adaption = new Adaption({
    height: 1080,
})).init();

// window.Highcharts = Highcharts;

import "uno.css";
import "@/public/styles";

import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

dayjs.locale("zh-cn");


const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}
app.use(ElementPlus, {
    locale: zhCn,
});
app.use(router);
app.use(store);
app.use(createPinia());
app.mount("#app");
app.component("ECharts", ECharts);

app.config.globalProperties.adaption = window.adaption;
