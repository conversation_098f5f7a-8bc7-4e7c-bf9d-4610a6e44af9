import { isDev } from "@/public/global/const";

if (isDev) {
    document.body.addEventListener("dblclick", (e) => {
        if (!e.target) {
            return;
        }
        const file = findFile(e.target);
        if (file) {
            console.log(file);
            window.fetch(`/__open-in-editor?file=${encodeURI(file)}`);
        }
    });
    function findFile(el) {
        const matchReg = /(src\/views)|(src\/view)|(src\/pages)|(src\/page)/;
        //vue2 __vue__
        //vue3 __vueParentComponent
        while (!(el.__vue__ || (el.__vnode && el.__vnode.ctx)) && el.parentElement) {
            el = el.parentElement;
        }
        if (!el) {
            return;
        }
        //vue2
        if (el.__vue__) {
            let comp = el.__vue__;
            while (!(comp.$options.__file && comp.$options.__file.match(matchReg)) && comp.parent) {
                comp = comp.parent;
            }
            return comp.$options && comp.$options.__file;
        }
        //vue3
        if (el.__vnode && el.__vnode.ctx) {
            let comp = el.__vnode.ctx;
            while (
                !(comp.type && comp.type.__file && comp.type.__file.match(matchReg)) &&
                comp.parent
            ) {
                comp = comp.parent;
            }
            return comp.type && comp.type.__file;
        }
    }
}
