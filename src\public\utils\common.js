import * as R from "ramda";
import { createApp } from "vue";
import dayjs from "dayjs";

export const isArray = R.is(Array);

export const isNumber = R.is(Number);

export const isFunction = R.is(Function);

export const isObject = R.is(Object);

export const isBoolean = R.is(Boolean);

export const isString = R.is(String);

export const isNotNil = R.pipe(R.isNil, R.not);

export const isNil = R.isNil;

export const allIsNotNil = R.all(isNotNil);

export const anyIsNotNil = R.any(isNotNil);

export const allIsNil = R.all(isNil);

export const anyIsNil = R.any(isNil);

export const mergeDeep = R.mergeDeepRight;

export const merge = R.mergeRight;

export const mergeDeepAll = R.mergeAll;

export const clone = R.clone;

export const equals = R.equals;

export const UUID = () => Math.random().toString(36).substr(2);

export function selectFile({ accept = "", file = false } = {}) {
    return new Promise((resolve, reject) => {
        let input = document.createElement("input");
        input.setAttribute("type", "file");
        input.setAttribute("accept", accept);
        input.onchange = () => {
            if (file) {
                resolve(input.files[0]);
            } else {
                const url = URL.createObjectURL(input.files[0]);
                resolve(url);
            }
            input = null;
        };
        input.onblur = () => {
            reject();
            input = null;
        };
        input.click();
    });
}

function objectURLToBlob(url) {
    return new Promise((resolve, reject) => {
        const http = new XMLHttpRequest();
        http.open("GET", url, true);
        http.responseType = "blob";
        http.onload = function () {
            if (this.status === 200 || this.status === 0) {
                resolve(this.response);
            } else {
                reject();
            }
        };
        http.send();
    });
}

export const readBlobUrl = async (url) => {
    const blob = await objectURLToBlob(url);
    return readFile(blob);
};

export const readFile = (file) => {
    if (!file) {
        return;
    }
    let reader = new FileReader();
    return new Promise((resolve) => {
        const handlerLoad = () => {
            resolve(reader.result);
            reader.removeEventListener("load", handlerLoad, false);
            reader = null;
        };
        reader.addEventListener("load", handlerLoad, false);
        reader.readAsDataURL(file);
    });
};

export const pathProp = R.useWith(R.call, [R.pipe(R.split("."), R.path), R.identity]);

export const pathObj = R.useWith(R.flip(R.reduce(R.pipe(R.flip(R.objOf)))), [
    R.pipe(R.split("."), R.reverse),
    R.identity,
]);

export function mixin(obj, sources) {
    //深拷贝
    if (isObject(sources)) {
        for (const key in sources) {
            if (isObject(sources[key])) {
                if (!isObject(obj[key])) {
                    obj[key] = {};
                }
                if (sources[key] instanceof Cesium.Color) {
                    obj[key] = sources[key];
                } else {
                    mixin(obj[key], sources[key]);
                }
            } else if (sources[key] !== undefined && !isFunction(sources[key])) {
                obj[key] = sources[key];
            }
        }
    }

    return obj;
}

export function mixins(obj) {
    if (arguments.length >= 2) {
        for (let i = 1; i < arguments.length; i++) {
            mixin(obj, arguments[i]);
        }
    }

    return obj;
}

export function objectToOptions(map) {
    if (isArray(map)) {
        return map.map((item) => ({
            label: item,
            value: item,
        }));
    }
    if (isObject(map)) {
        return Object.entries(map).map(([key, value]) => {
            return {
                label: value,
                value: key,
            };
        });
    }
    console.log(`objectToOptions不支持这种类型的数据`, map);
}

export const exportFile = (data, name) => {
    if (isObject(data)) {
        data = JSON.stringify(data);
    }
    const blob = new Blob([data], { type: "application/octet-stream" });
    const blobURL = window.URL.createObjectURL(blob);
    const tempLink = document.createElement("a");
    tempLink.href = blobURL;
    tempLink.setAttribute("download", name);
    tempLink.click();
};

export const downloadFile = (blobURL, name) => {
    const tempLink = document.createElement("a");
    tempLink.href = blobURL + "?origin=1";
    tempLink.setAttribute("download", name);
    tempLink.setAttribute("target", "_blank");
    document.body.appendChild(tempLink);
    tempLink.click();
};

export const copy = (text) => {
    if (isObject(text)) {
        text = JSON.stringify(text, null, 4);
    }
    if (!isString(text)) {
        return;
    }
    let textArea = document.createElement("textArea");
    document.body.appendChild(textArea);
    textArea.innerHTML = text;
    textArea.select();
    document.execCommand("copy");
    document.body.removeChild(textArea);
};

export const debounce = function (fn, delay) {
    let timerId;
    return function (...args) {
        if (timerId) {
            clearTimeout(timerId);
        }
        timerId = setTimeout(() => {
            fn(...args);
            timerId = null;
        }, delay);
    };
};

export function renderComponent(c, props) {
    const app = createApp(c, props);
    const div = document.createElement("div");
    app.mount(div);
    div.app = app._instance && app._instance.exposeProxy;
    return div;
}

/**
 * 滚动到指定元素
 *
 * @export
 * @param {any} container
 * @param {any} selected
 * @returns
 */
export function scrollIntoView(container, selected) {
    if (!selected) {
        container.scrollTop = 0;
        return;
    }

    const top = selected.offsetTop;
    const bottom = selected.offsetTop + selected.offsetHeight;
    const viewRectTop = container.scrollTop;
    const viewRectBottom = viewRectTop + container.clientHeight;

    if (top < viewRectTop) {
        container.scrollTop = top;
    } else if (bottom > viewRectBottom) {
        container.scrollTop = bottom - container.clientHeight;
    }
}

/**
 * 滚到到指定高度
 *
 * @export
 * @param {Number} _to
 * @param {Number} _time
 * @param {String} direction
 * @param {function} callback
 */
export function scrollTo(container, _to, _time, direction = "scrollTop", callback) {
    let scrollFrom = parseInt(container[direction]),
        i = 0,
        runEvery = 5;
    let to = _to;
    let time = _time / runEvery;
    let interval = setInterval(() => {
        i++;
        container[direction] = ((to - scrollFrom) / time) * i + scrollFrom;
        if (i >= time) {
            if (callback) callback();
            clearInterval(interval);
        }
    }, runEvery);
}

export const formatTime = (time, template = "YYYY-MM-DD HH:mm:ss") => {
    if (!time) {
        return "";
    }
    return dayjs(time).format(template);
};

export function randomNum(n) {
    let t = "";
    for (var i = 0; i < n; i++) {
        t += Math.ceil(Math.random() * 9);
    }
    if (t.length !== n) {
        return randomNum(n);
    }
    return t;
}

export function getFileSize(file) {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = function () {
            resolve({ width: this.width, height: this.height });
        };
        img.src = URL.createObjectURL(file);
    });
}
