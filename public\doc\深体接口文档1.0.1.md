

### 服务器地址http://192.168.5.39:24122

### 1.应急演练

#### 1.1新增应急演练

##### 1.1.1 接口地址  /yjyl/save

##### 1.1.2 请求方式 post

##### 1.1.3请求参数

参数说明

| 字段名    | 含义               | 示例                       |
| --------- | ------------------ | -------------------------- |
| name      | 名称               |                            |
| level     | 事件等级           |                            |
| type      | 事件类型           |                            |
| addr      | 事件位置           |                            |
| rypz      | 人员配置           |                            |
| clpz      | 车辆配置           |                            |
| zawpz     | 障碍物配置         |                            |
| crkpz     | 出入口配置         |                            |
| afsbpz    | 安防设备配置       |                            |
| dzpz      | 道闸配置           |                            |
| ssys      | 疏散用时           |                            |
| ssrs      | 疏散人数           |                            |
| zlrs      | 驻留人数           |                            |
| gckljssrs | 各出口累计疏散人数 |                            |
| startTime | 开始时间           |                            |
| endTime   | 结束时间           |                            |
| id        | 主键               | 新增时不传Id，修改时传入Id |

示例

```java
{
  "addr": "地址",
  "afsbpz": "[{\"name\":\"111\"}]",
  "clpz": "[{\"name\":\"111\"}]",
  "crkpz": "[{\"name\":\"111\"}]",
  "dzpz": "[{\"name\":\"111\"}]",
  "entTime": "2025-02-24 02:49:57",
  "gckljssrs": "[{\"name\":\"111\"}]",
  "level": "1",
  "name": "疏散演练1",
  "rypz": "[{\"name\":\"111\"}]",
  "ssrs": 110,
  "ssys": "10:10:10",
  "startTime": "2025-02-24 02:49:57",
  "type": "火灾",
  "zawpz": "[{\"name\":\"111\"}]",
  "zlrs": "string"
}
```

##### 1.1.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": null,
    "total": null
}
```

#### 1.2.列表查询应急演练

##### 1.2.1 接口地址  /yjyl/list

##### 1.2.2 请求方式 get

##### 1.2.3请求参数

| 字段           | 含义         | 示例                |
| -------------- | ------------ | ------------------- |
| pageNum        | 页码         | 1                   |
| pageSize       | 每页显示条数 | 10                  |
| name           | 名称         |                     |
| type           | 类型         |                     |
| queryStartTime | 查询开始时间 | 2025-02-24 02:49:57 |
| queryEndTime   | 查询结束时间 | 2025-02-24 02:49:57 |

示例

```java
/yjyl/list?pageSize=1&pageNum=1
```

##### 1.2.4 请求返回值

```java
{
    "code": 200,
    "msg": "查询成功",
    "data": [
        {
            "id": 1,
            "name": "疏散演练1",
            "level": "1",
            "type": "火灾",
            "addr": "地址",
            "rypz": "[{\"name\":\"111\"}]",
            "clpz": "[{\"name\":\"111\"}]",
            "zawpz": "[{\"name\":\"111\"}]",
            "crkpz": "[{\"name\":\"111\"}]",
            "afsbpz": "[{\"name\":\"111\"}]",
            "dzpz": "[{\"name\":\"111\"}]",
            "ssys": "10:10:10",
            "ssrs": 110,
            "zlrs": "string",
            "gckljssrs": "[{\"name\":\"111\"}]",
            "startTime": "2025-02-24 02:49:57",
            "endTime": "2025-02-24 02:49:57"
        }
    ],
    "total": 2
}
```

#### 1.3.删除应急演练记录

##### 1.3.1 接口地址  /yjyl/delete?ids=1

##### 1.3.2 请求方式 get

##### 1.3.3请求参数

示例

```java
/yjyl/delete?ids=1
```

##### 1.3.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": null,
    "total": null
}
```

#### 1.4.查询应急演练详情

##### 1.4.1 接口地址  /yjyl/queryById

##### 1.4.2 请求方式 get

##### 1.4.3请求参数

示例

```java
/yjyl/queryById?id=1
```

##### 1.4.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "name": "疏散演练1",
        "level": "1",
        "type": "火灾",
        "addr": "地址",
        "rypz": "[{\"name\":\"111\"}]",
        "clpz": "[{\"name\":\"111\"}]",
        "zawpz": "[{\"name\":\"111\"}]",
        "crkpz": "[{\"name\":\"111\"}]",
        "afsbpz": "[{\"name\":\"111\"}]",
        "dzpz": "[{\"name\":\"111\"}]",
        "ssys": "10:10:10",
        "ssrs": 110,
        "zlrs": "string",
        "gckljssrs": "[{\"name\":\"111\"}]",
        "startTime": "2025-02-24 02:49:57",
        "endTime": "2025-02-24 02:49:57"
    },
    "total": null
}
```

#### 1.1导出应急演练

##### 1.1.1 接口地址  /yjyl/export

##### 1.1.2 请求方式 post

##### 1.1.3请求参数

参数说明

| 字段名    | 含义               | 示例 |
| --------- | ------------------ | ---- |
| name      | 名称               |      |
| level     | 事件等级           |      |
| type      | 事件类型           |      |
| addr      | 事件位置           |      |
| rypz      | 人员配置           |      |
| clpz      | 车辆配置           |      |
| zawpz     | 障碍物配置         |      |
| crkpz     | 出入口配置         |      |
| afsbpz    | 安防设备配置       |      |
| dzpz      | 道闸配置           |      |
| ssys      | 疏散用时           |      |
| ssrs      | 疏散人数           |      |
| zlrs      | 驻留人数           |      |
| gckljssrs | 各出口累计疏散人数 |      |
| startTime | 开始时间           |      |
| endTime   | 结束时间           |      |

示例

```java
{
  "addr": "地址",
  "afsbpz": "[{\"name\":\"111\"}]",
  "clpz": "[{\"name\":\"111\"}]",
  "crkpz": "[{\"name\":\"111\"}]",
  "dzpz": "[{\"name\":\"111\"}]",
  "entTime": "2025-02-24 02:49:57",
  "gckljssrs": "[{\"name\":\"111\"}]",
  "level": "1",
  "name": "疏散演练1",
  "rypz": "[{\"name\":\"111\"}]",
  "ssrs": 110,
  "ssys": "10:10:10",
  "startTime": "2025-02-24 02:49:57",
  "type": "火灾",
  "zawpz": "[{\"name\":\"111\"}]",
  "zlrs": "string"
}
```

##### 1.1.4 请求返回值

返回文件

### 2.应急预案

#### 2.1新增应急预案

##### 2.1.1 接口地址  /yjya/save

##### 2.1.2 请求方式 post

##### 2.1.3请求参数

参数说明

| 字段名    | 含义               | 示例                       |
| --------- | ------------------ | -------------------------- |
| name      | 名称               |                            |
| level     | 事件等级           |                            |
| type      | 事件类型           |                            |
| addr      | 事件位置           |                            |
| rypz      | 人员配置           |                            |
| clpz      | 车辆配置           |                            |
| zawpz     | 障碍物配置         |                            |
| crkpz     | 出入口配置         |                            |
| afsbpz    | 安防设备配置       |                            |
| dzpz      | 道闸配置           |                            |
| ssys      | 疏散用时           |                            |
| ssrs      | 疏散人数           |                            |
| zlrs      | 驻留人数           |                            |
| gckljssrs | 各出口累计疏散人数 |                            |
| startTime | 开始时间           |                            |
| endTime   | 结束时间           |                            |
| id        | 主键               | 新增时不传Id，修改时传入Id |
| yaType    | 预案类型           |                            |
| fileUrl   | 预案文件           |                            |

示例

```java
{
  "addr": "地址",
  "afsbpz": "[{\"name\":\"111\"}]",
  "clpz": "[{\"name\":\"111\"}]",
  "crkpz": "[{\"name\":\"111\"}]",
  "dzpz": "[{\"name\":\"111\"}]",
  "entTime": "2025-02-24 02:49:57",
  "gckljssrs": "[{\"name\":\"111\"}]",
  "level": "1",
  "name": "疏散演练1",
  "rypz": "[{\"name\":\"111\"}]",
  "ssrs": 110,
  "ssys": "10:10:10",
  "startTime": "2025-02-24 02:49:57",
  "type": "火灾",
  "zawpz": "[{\"name\":\"111\"}]",
  "zlrs": "string"
}
```

##### 2.1.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": null,
    "total": null
}
```

#### 2.2.列表查询预案演练

##### 2.2.1 接口地址  /yjya/list

##### 2.2.2 请求方式 get

##### 2.2.3请求参数

| 字段     | 含义         | 示例 |
| -------- | ------------ | ---- |
| pageNum  | 页码         | 1    |
| pageSize | 每页显示条数 | 10   |
| name     | 名称         |      |
| type     | 事件类型     |      |
| yaType   | 预案类型     |      |

示例

```java
/yjya/list?pageSize=1&pageNum=1
```

##### 2.2.4 请求返回值

```java
{
    "code": 200,
    "msg": "查询成功",
    "data": [
        {
            "id": 1,
            "name": "疏散演练1",
            "level": "1",
            "type": "火灾",
            "addr": "地址",
            "rypz": "[{\"name\":\"111\"}]",
            "clpz": "[{\"name\":\"111\"}]",
            "zawpz": "[{\"name\":\"111\"}]",
            "crkpz": "[{\"name\":\"111\"}]",
            "afsbpz": "[{\"name\":\"111\"}]",
            "dzpz": "[{\"name\":\"111\"}]",
            "ssys": "10:10:10",
            "ssrs": 110,
            "zlrs": "string",
            "gckljssrs": "[{\"name\":\"111\"}]",
            "startTime": "2025-02-24 02:49:57",
            "endTime": "2025-02-24 02:49:57"
        }
    ],
    "total": 2
}
```

#### 2.3.删除预案演练记录

##### 2.3.1 接口地址  /yjya/delete?ids=1

##### 2.3.2 请求方式 get

##### 2.3.3请求参数

示例

```java
/yjya/delete?ids=1
```

##### 2.3.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": null,
    "total": null
}
```

#### 2.4.查询预案演练详情

##### 2.4.1 接口地址  /yjya/queryById

##### 2.4.2 请求方式 get

##### 2.4.3请求参数

示例

```java
/yjya/queryById?id=1
```

##### 2.4.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "name": "疏散演练1",
        "level": "1",
        "type": "火灾",
        "addr": "地址",
        "rypz": "[{\"name\":\"111\"}]",
        "clpz": "[{\"name\":\"111\"}]",
        "zawpz": "[{\"name\":\"111\"}]",
        "crkpz": "[{\"name\":\"111\"}]",
        "afsbpz": "[{\"name\":\"111\"}]",
        "dzpz": "[{\"name\":\"111\"}]",
        "ssys": "10:10:10",
        "ssrs": 110,
        "zlrs": "string",
        "gckljssrs": "[{\"name\":\"111\"}]",
        "startTime": "2025-02-24 02:49:57",
        "endTime": "2025-02-24 02:49:57"
    },
    "total": null
}
```

### 3.监控设备

#### 3.1新增监控设备

##### 3.1.1 接口地址  /device/save

##### 3.1.2 请求方式 post

##### 3.1.3请求参数

参数说明

| 字段名     | 含义     | 示例                       |
| ---------- | -------- | -------------------------- |
| code       | 设备编号 |                            |
| name       | 设备名称 |                            |
| type       | 设备类型 |                            |
| lnglat     | 经纬度   |                            |
| status     | 运行状态 |                            |
| openStatus | 开关状态 |                            |
| id         | 主键     | 新增时不传Id，修改时传入Id |

示例

```java
{
  "code": "sdsdas1d21212as",
  "lnglat": "120.12121,30.4545",
  "status": "开",
  "openStatus": "管",
  "name": "视频设备1",
  "type": "球机"
}
```

##### 3.1.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": null,
    "total": null
}
```

#### 3.2.列表查询监控设备

##### 3.2.1 接口地址  /device/list

##### 3.2.2 请求方式 get

##### 3.2.3请求参数

| 字段     | 含义         | 示例 |
| -------- | ------------ | ---- |
| pageNum  | 页码         | 1    |
| pageSize | 每页显示条数 | 10   |
| name     | 名称         |      |
| type     | 类型         |      |

示例

```java
/device/list?pageSize=1&pageNum=1
```

##### 3.2.4 请求返回值

```java
{
    "code": 200,
    "msg": "查询成功",
    "data": [
        {
            "id": 1,
            "code": "121212",
            "name": "名称",
            "type": "类型",
            "lnglat": "1",
            "status": "1",
            "openStatus": "1"
        },
        {
            "id": 2,
            "code": "sdsdas1d21212as",
            "name": "视频设备1",
            "type": "球机",
            "lnglat": "120.12121,30.4545",
            "status": "开",
            "openStatus": "管"
        }
    ],
    "total": 2
}
```

#### 3.3.删除监控设备

##### 3.3.1 接口地址  /device/delete?ids=1

##### 3.3.2 请求方式 get

##### 3.3.3请求参数

示例

```java
/device/delete?ids=1
```

##### 3.3.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": null,
    "total": null
}
```

#### 3.4.查询监控设备详情

##### 3.4.1 接口地址  /device/queryById

##### 3.4.2 请求方式 get

##### 3.4.3请求参数

示例

```java
/device/queryById?id=1
```

##### 3.4.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "code": "121212",
        "name": "名称",
        "type": "类型",
        "lnglat": "1",
        "status": "1",
        "openStatus": "1"
    },
    "total": null
}
```

### 4.安全出口

#### 4.1新增安全出口

##### 4.1.1 接口地址  /aqck/save

##### 4.1.2 请求方式 post

##### 4.1.3请求参数

参数说明

| 字段名 | 含义     | 示例                       |
| ------ | -------- | -------------------------- |
| name   | 名称     |                            |
| lnglat | 坐标     |                            |
| type   | 事件类型 |                            |
| id     | 主键     | 新增时不传Id，修改时传入Id |

示例

```java
{
  "lnglat": "120.12121,30.4545",
  "name": "出口1",
  "type": "类型1"
}
```

##### 4.1.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": null,
    "total": null
}
```

#### 4.2.列表查询安全出口

##### 4.2.1 接口地址  /aqck/list

##### 4.2.2 请求方式 get

##### 4.2.3请求参数

| 字段     | 含义         | 示例 |
| -------- | ------------ | ---- |
| pageNum  | 页码         | 1    |
| pageSize | 每页显示条数 | 10   |
| name     | 名称         |      |
| type     | 类型         |      |

示例

```java
/aqck/list?pageSize=1&pageNum=1
```

##### 4.2.4 请求返回值

```java
{
    "code": 200,
    "msg": "查询成功",
    "data": [
        {
            "id": 1,
            "name": "名称",
            "type": "类型",
            "lnglat": "120.121212,30.121212"
        }
    ],
    "total": 2
}
```

#### 4.3.删除安全出口

##### 4.3.1 接口地址  /aqck/delete?ids=1

##### 4.3.2 请求方式 get

##### 4.3.3请求参数

示例

```java
/aqck/delete?ids=1
```

##### 4.3.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": null,
    "total": null
}
```

#### 4.4.查询安全出口详情

##### 4.4.1 接口地址  /aqck/queryById

##### 4.4.2 请求方式 get

##### 4.4.3请求参数

示例

```java
/aqck/queryById?id=1
```

##### 4.4.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "name": "名称",
        "type": "类型",
        "lnglat": "120.121212,30.121212"
    },
    "total": null
}
```

### 5.文件相关

#### 5.1上传文件

##### 5.1.1 接口地址  /upload

##### 5.1.2 请求方式 post

##### 5.1.3请求参数

参数说明

| 字段名 | 含义 | 示例 |
| ------ | ---- | ---- |
| file   | 文件 |      |

示例

```java
无
```

##### 5.1.4 请求返回值

```java
{
    "code": 200,
    "msg": "操作成功",
    "data": "20250226134524/抄表数据上传模板.xls",
    "total": null
}
```

#### 5.2.下载文件

##### 5.2.1 接口地址  /download

##### 5.2.2 请求方式 get

##### 5.2.3请求参数

| 字段     | 含义 | 示例 |
| -------- | ---- | ---- |
| fileName | 页码 | 1    |

示例

```java
/download?fileName=20250226134524/抄表数据上传模板.xls
```

##### 5.2.4 请求返回值

```java
返回二进制流
```
