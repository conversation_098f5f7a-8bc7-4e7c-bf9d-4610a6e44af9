import dayJs from 'dayJs';
var data1 = {
  code: 'stadiumReserveStatistics',
  titleList: [
    {
      name: 'stadium_name',
      type: 'string',
      label: '场馆名称',
    },
    {
      name: 'dt',
      type: 'string',
      label: '时间',
    },
    {
      name: 'num',
      type: 'int',
      label: '预约数量',
    },
  ],
  spentMillis: 1927,
  success: true,
  message: '成功',
  data: [
    {
      stadium_name: '深圳市体育馆',
      dt: '2025-04',
      num: 1290,
    },
    {
      stadium_name: '迪兰朵体育馆',
      dt: '2025-04',
      num: 5628,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2025-03',
      num: 2861,
    },
    {
      stadium_name: '迪兰朵体育馆',
      dt: '2025-03',
      num: 12487,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2025-02',
      num: 2580,
    },
    {
      stadium_name: '迪兰朵体育馆',
      dt: '2025-02',
      num: 11256,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2025-01',
      num: 2853,
    },
    {
      stadium_name: '迪兰朵体育馆',
      dt: '2025-01',
      num: 12716,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2024-12',
      num: 2860,
    },
    {
      stadium_name: '迪兰朵体育馆',
      dt: '2024-12',
      num: 12516,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2024-11',
      num: 2763,
    },
    {
      stadium_name: '迪兰朵体育馆',
      dt: '2024-11',
      num: 12314,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2024-10',
      num: 2853,
    },
    {
      stadium_name: '迪兰朵体育馆',
      dt: '2024-10',
      num: 12694,
    },
    {
      stadium_name: '迪兰朵体育馆',
      dt: '2024-09',
      num: 2383,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2024-09',
      num: 2486,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2024-08',
      num: 2473,
    },
    {
      stadium_name: '深圳市体育馆',
      dt: '2024-07',
      num: 2315,
    },
  ],
  title: '场馆预订情况',
};

// 定义函数，用于处理原始数据
export const processData = function(data) {
  const result = {};

  // 遍历原始数据
  data.data.forEach((item) => {
    const {dt: time, stadium_name: stadium, num: count} = item;
    if (!result[time]) {
      result[time] = {};
    }
    result[time][stadium] = count;
  });

  // 将对象转换为数组
  let arr = [];
  let lengend = [];
  for(let i in data) {
    let obj = {
      time: i,
    }
    let index = 0;
    for(let j in data[i]) {
      obj['value' + (index + 1)] = data[i][j];
      index++;
      if(!lengend.includes(j)) {
        lengend.push(j); 
      }
    }
    arr.push(obj);
  }

  // return {
  //   data: arr,
  //   lengend: lengend,
  // }

  return result;
};

export const processData1 = function(dataObj) {
  const timeMap = {};

  // 遍历原始数据
  dataObj.data.forEach((item) => {
    const {dt: time, stadium_name: stadium, num: count} = item;
    if (!timeMap[time]) {
      timeMap[time] = {
        time,
        ...dataObj.titleList.slice(1).reduce((acc, {name}) => {
          if (name !== 'dt') acc[name] = {};
          return acc;
        }, {}),
      };
    }
    timeMap[time].num[stadium] = count;
  });
  // 将对象转换为数组
  const resultArray = Object.values(timeMap);

  return resultArray;
};

//根据类型获取时间参数  天，周，月，季度
export const getTimeParams = function(value) {
  let up_day = 1;
  if (value == "day") {
    up_day = 1;
  } else if (value == "week") {
    up_day = 7;
  } else {
    up_day = 30;
  }
  let end_time = dayJs().format("YYYY-MM-DD");
  let start_time = dayJs().subtract(up_day, 'day').format("YYYY-MM-DD"); 
  return {
    start_time,
    end_time,
  }
}

/**
 * 根据接口数据获取legend和echart数据
 * @param {*} data 接口返回数据 
 * @param {*} lengend_key  legend的key
 * @param {*} x_key  x轴的key
 * @param {*} y_key  y轴的key
 * @returns
 */
export function getLengendAndEchartData(data,lengend_key,x_key,y_key) {
  let legend = [];
  let chartData = [];
  data.map((item) => {
    if(typeof(lengend_key) !== 'string') {
      legend = lengend_key;
    } else if (!legend.includes(item[lengend_key])) {
      legend.push(item[lengend_key]);
    }
    
    if (chartData.findIndex((it) => it.time == item[x_key]) == -1) {
      chartData.push({ time: item[x_key] });
    }
  });
  data.map(item=>{
    let key = legend.findIndex(it=>it == item[lengend_key]);
    chartData.find(it=>it.time == item[x_key])['value'+(key+1)] = item[y_key];
  })

  return {
    legend,
    chartData,
  }
}