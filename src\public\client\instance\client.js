/*
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-16 16:13:16
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-04-28 10:15:34
 * @FilePath: \chengde-computer-room\src\public\client\instance\client.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from "axios";
import { AppGlobalConfig } from "@/public/global/const";
import errorCode from "@/utils/errorCode";

// axios.defaults.headers.post['Content-Type'] = 'application/json';
const instance = axios.create({
    baseURL: AppGlobalConfig.ServerUrl,
});

// axios.defaults.withCredentials = true;

instance.interceptors.request.use(async (config) => {
    config.headers["Authorization"] = localStorage.getItem("token") || AppGlobalConfig.token;
    return config;
});

instance.interceptors.response.use(
    (response) => {
        const { data } = response;
        if (data.code === 200) {
            return data;
        } else {
            return Promise.reject({
                msg: data.msg,
            });
        }
    },
    () => {
        return Promise.reject("服务器繁忙！");
    },
);

// 创建axios实例  大数据
export const instanceXc = axios.create({
    // axios中请求配置有baseURL选项，表示请求URL公共部分
    baseURL: window.AppGlobalConfig.XcUrl,
    // baseURL: window.AppGlobalConfig.dsjUrl,
    // 超时
    timeout: 10000,
});

// request拦截器
instanceXc.interceptors.request.use(
    (config) => {
        // 是否需要设置 token
        // const isToken = (config.headers || {}).isToken === false
        // if (getToken() && !isToken) {
        config.headers["Authorization"] = "test"; // 让每个请求携带自定义token 请根据实际情况自行修改

        config.data = {
            body: config.data,
            requrl: window.AppGlobalConfig.dsjUrl + config.url,
            reqtype: config.method,
        };
        config.url = "";
        // }
        return config;
    },
    (error) => {
        console.log(error);
        Promise.reject(error);
    },
);

// 响应拦截器
instanceXc.interceptors.response.use(
    (res) => {
        // 未设置状态码则默认成功状态
        const code = res.data.code || 200;
        // 获取错误信息
        const msg = errorCode[code] || res.data.msg || errorCode["default"];
        // 二进制数据则直接返回
        if (res.request.responseType === "blob" || res.request.responseType === "arraybuffer") {
            return res.data;
        }
        if (code == 401) {
            // Message({ message: msg, type: 'error' })
            return Promise.reject(new Error(msg));
        } else if (code == 500) {
            // Message({ message: msg, type: 'error' })
            return Promise.reject(new Error(msg));
        } else if (code == 601) {
            // Message({ message: msg, type: 'warning' })
            return Promise.reject("error");
        } else if (code == 200 || code === "0") {
            return res.data;
        } else {
            // Notification.error({ title: msg })
            return Promise.reject("error");
        }
    },
    (error) => {
        console.log("err" + error);
        // let { message } = error;
        // if (message == "Network Error") {
        //     message = "后端接口连接异常";
        // } else if (message.includes("timeout")) {
        //     message = "系统接口请求超时";
        // } else if (message.includes("Request failed with status code")) {
        //     message = "系统接口" + message.substr(message.length - 3) + "异常";
        // }
        // Message({ message: message, type: 'error', duration: 5 * 1000 })
        return Promise.reject(error);
    },
);

export default instance;
