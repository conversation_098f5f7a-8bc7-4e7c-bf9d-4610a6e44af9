<template>
    <router-view v-slot="{ Component }">
        <LeftMenu />
        <component :is="Component" />
    </router-view>
</template>

<script setup>
import LeftMenu from "@/components/LeftMenu";
import { useStore } from "vuex";
import { onMounted, ref, provide } from "vue";
import { listKey } from "@/apis";

const store = useStore();

// 字典数据
const dictData = ref([]);

// 提供字典数据给子组件
provide("dictData", dictData);

// 获取字典数据
const getDictData = async () => {
    try {
        const res = await listKey();
        if (res && res.code === 200) {
            dictData.value = res.data || [];
        }
    } catch (error) {
        console.error("获取字典数据失败：", error);
    }
};

onMounted(() => {
    // 获取字典数据
    getDictData();

    // 定时刷新数据
    setInterval(() => {
        store.commit("setUpdate");
    }, 100000);
});
</script>

<style lang="scss" scoped></style>
