module.exports = {
    root: true,
    env: {
        browser: true, // browser global variables
        es2021: true, // adds all ECMAScript 2021 globals and automatically sets the ecmaVersion parser option to 12.
    },
    extends: [
        "./.eslintrc-auto-import.json",
        "plugin:vue/vue3-recommended",
        "plugin:prettier/recommended", // ++
    ],
    "parserOptions": {
        "parser": "@babel/eslint-parser",
        "ecmaVersion": 12,
        "sourceType": "module"
    },
    plugins: ["prettier"],
    rules: {
        "prettier/prettier": "error",
        "no-unused-vars": 1,
        "vue/multi-word-component-names": 0,
        "vue/require-default-prop": 0,
    },
};
