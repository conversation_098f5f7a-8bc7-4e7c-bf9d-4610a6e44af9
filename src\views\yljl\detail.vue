<template>
    <div class="right">
        <BaseTitle />
        <div class="body">
            <div class="title">演练记录详情</div>
            <div class="detail-container">
                <div class="back-btn">
                    <el-button @click="goBack">返回</el-button>
                </div>

                <!-- 基本信息 -->
                <div class="detail-section">
                    <div class="section-title">基本信息</div>
                    <div class="detail-form">
                        <div class="form-row">
                            <div class="form-item">
                                <label>演练名称：</label>
                                <span>{{ drillDetail.drillName || "-" }}</span>
                            </div>
                            <div class="form-item">
                                <label>事件类型：</label>
                                <span>{{ getEventTypeName(drillDetail.drillEventType) }}</span>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-item">
                                <label>计划演练时间：</label>
                                <span>{{ formatTime(drillDetail.plannedDrillTime) }}</span>
                            </div>
                            <div class="form-item">
                                <label>计划演练地点：</label>
                                <span>{{ drillDetail.plannedDrillLocation || "-" }}</span>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-item">
                                <label>演练状态：</label>
                                <el-tag
                                    :type="drillDetail.drillStatus === 1 ? 'success' : 'warning'"
                                >
                                    {{ drillDetail.drillStatus === 1 ? "已完成" : "待演练" }}
                                </el-tag>
                            </div>
                            <div class="form-item">
                                <label>应急预案ID：</label>
                                <span>{{ drillDetail.emergencyPlanId || "-" }}</span>
                            </div>
                        </div>
                        <div class="form-row full-width">
                            <div class="form-item">
                                <label>演练目的：</label>
                                <span>{{ drillDetail.drillPurpose || "-" }}</span>
                            </div>
                        </div>
                        <div class="form-row full-width">
                            <div class="form-item">
                                <label>备注：</label>
                                <span>{{ drillDetail.remark || "-" }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实际演练信息（仅已完成的演练显示） -->
                <div class="detail-section" v-if="drillDetail.drillStatus === 1">
                    <div class="section-title">实际演练信息</div>
                    <div class="detail-form">
                        <div class="form-row">
                            <div class="form-item">
                                <label>实际演练时间：</label>
                                <span>{{ formatTime(drillDetail.actualDrillTime) }}</span>
                            </div>
                            <div class="form-item">
                                <label>实际演练地点：</label>
                                <span>{{ drillDetail.actualDrillLocation || "-" }}</span>
                            </div>
                        </div>
                        <div class="form-row full-width">
                            <div class="form-item">
                                <label>演练总结文档：</label>
                                <div class="document-container">
                                    <span>{{ drillDetail.drillSummaryDoc || "-" }}</span>
                                    <el-button
                                        v-if="drillDetail.drillSummaryDoc"
                                        type="primary"
                                        size="small"
                                        @click="downloadDocument"
                                        class="download-btn"
                                    >
                                        下载
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间记录 -->
                <div class="detail-section">
                    <div class="section-title">时间记录</div>
                    <div class="detail-form">
                        <div class="form-row">
                            <div class="form-item">
                                <label>创建时间：</label>
                                <span>{{ formatTime(drillDetail.createTime) }}</span>
                            </div>
                            <div class="form-item">
                                <label>更新时间：</label>
                                <span>{{ formatTime(drillDetail.updateTime) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, inject, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import BaseTitle from "@/components/BaseTitle/index.vue";
import { DrillList, DrillDownload } from "@/apis";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";

const router = useRouter();
const route = useRoute();

// 获取全局字典数据
const dictData = inject("dictData");

// 事件类型字典数据
const eventTypeDictionary = computed(() => {
    return dictData.value.map((item) => ({
        name: item.dictValue,
        value: item.dictCode || item.id,
    }));
});

// 演练详情数据
const drillDetail = ref({});

// 获取演练详情
const getDrillDetail = async () => {
    const id = route.query.id;
    if (!id) {
        ElMessage.error("缺少演练ID参数");
        return;
    }

    try {
        // 通过列表接口获取单个演练的详情
        const res = await DrillList({ id });
        if (res && res.code === 200 && res.data && res.data.length > 0) {
            drillDetail.value = res.data[0];
        } else {
            ElMessage.error("获取演练详情失败");
        }
    } catch (error) {
        console.error("获取演练详情失败：", error);
        ElMessage.error("获取演练详情失败");
    }
};

// 格式化时间
const formatTime = (timeString) => {
    if (!timeString) return "-";
    return dayjs(timeString).format("YYYY-MM-DD HH:mm:ss");
};

// 获取事件类型名称
const getEventTypeName = (typeValue) => {
    const eventType = eventTypeDictionary.value.find((type) => type.value === typeValue);
    return eventType ? eventType.name : "其他";
};

// 下载文档
const downloadDocument = async () => {
    if (!drillDetail.value.drillSummaryDoc) {
        ElMessage.warning("没有可下载的文档");
        return;
    }

    try {
        const fileName = drillDetail.value.drillSummaryDoc;
        const response = await DrillDownload({ fileName });

        // // 创建下载链接
        // const blob = new Blob([response], { type: "application/octet-stream" });
        // const url = window.URL.createObjectURL(blob);
        // const link = document.createElement("a");
        // link.href = url;

        // // 从文件名中提取实际文件名（去掉路径）
        // const actualFileName = fileName.split("/").pop() || fileName;
        // link.download = actualFileName;

        // document.body.appendChild(link);
        // link.click();
        // document.body.removeChild(link);
        // window.URL.revokeObjectURL(url);

        ElMessage.success("文档下载成功");
    } catch (error) {
        console.error("下载文档失败：", error);
        ElMessage.error("下载文档失败");
    }
};

// 返回上一页
const goBack = () => {
    router.back();
};

onMounted(() => {
    getDrillDetail();
});
</script>

<style scoped lang="scss">
.right {
    width: calc(100% - 336px);
}

.body {
    padding: 50px;
    height: calc(100% - 122px);

    .title {
        margin-bottom: 30px;
        font-size: 20px;
        font-weight: bold;
        color: #303133;
    }
}

.detail-container {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    padding: 30px;
}

.back-btn {
    margin-bottom: 24px;
}

.detail-section {
    margin-bottom: 32px;

    &:last-child {
        margin-bottom: 0;
    }

    .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid #f0f2f5;
    }
}

.detail-form {
    .form-row {
        display: flex;
        margin-bottom: 20px;

        &.full-width {
            .form-item {
                width: 100%;
            }
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    .form-item {
        flex: 1;
        margin-right: 40px;

        &:last-child {
            margin-right: 0;
        }

        label {
            font-weight: 500;
            color: #606266;
            margin-right: 12px;
            min-width: 120px;
            display: inline-block;
        }

        span {
            color: #303133;
            line-height: 1.5;
        }

        .document-container {
            display: flex;
            align-items: center;
            gap: 12px;

            .download-btn {
                flex-shrink: 0;
            }
        }
    }
}

@media (max-width: 768px) {
    .detail-form {
        .form-row {
            flex-direction: column;

            .form-item {
                margin-right: 0;
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}
</style>
