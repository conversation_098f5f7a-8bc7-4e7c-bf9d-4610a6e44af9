import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { createHtmlPlugin } from "vite-plugin-html";
import Unocss from "unocss/vite";
import postCssPxToRem from "postcss-pxtorem";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import VitePluginUseState from "./plugins/VitePluginUseState.js";

const pathSrc = path.resolve(__dirname, "src");
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    const isProduction = mode === "production";
    return {
        define: {},
        base: "",
        server: {
            host: "0.0.0.0",
            port: "9527",
            proxy: {
                "/api": {
                    target: "http://************:24122/",
                    changeOrigin: true, // 允许跨域
                    rewrite: (path) => path.replace(/^\/api/, ""), // 重写请求路径
                },
                "/xc": {
                    target: "http://ioc.sztyzx.com.cn/open/api/common/relay",
                    changeOrigin: true, // 允许跨域
                    rewrite: (path) => path.replace(/^\/xc/, ""), // 重写请求路径
                },
                // "/yjyl": {
                //     target: "http://************:24122",
                // },
            },
        },
        plugins: [
            VitePluginUseState(),
            vue({
                script: {
                    defineModel: true,
                },
            }),
            createHtmlPlugin({
                inject: {
                    data: { isProduction },
                },
            }),
            {
                name: "vite:html_fix",
                enforce: "pre",
                configureServer(server) {
                    //覆盖 vite-plugin-html 中间件的 rewrites
                    server.middlewares.use((req, res, next) => {
                        req.url = req.originalUrl;
                        next();
                    });
                },
            },
            Unocss({
                postprocess(util) {
                    const remRE = /^-?[\.\d]+rem/;
                    util.entries.forEach((i) => {
                        const value = i[1];
                        if (value && typeof value === "string" && remRE.test(value))
                            i[1] = i[1].replace(
                                remRE,
                                `${(value.match(/^-?[\.\d]+rem/)[0].slice(0, -3) * 4) / 100}rem`,
                            );
                    });
                },
            }),
            AutoImport({
                resolvers: [],
                imports: ["vue", "pinia", "vue-router", "@vueuse/core"],
                dts: true,
                eslintrc: {
                    enabled: true,
                },
                dirs: [path.resolve(__dirname, "./src/public/utils/hook.**")],
            }),
            Components({
                resolvers: [],
                dts: true,
            }),
        ],
        resolve: {
            alias: {
                "@/": `${pathSrc}/`,
            },
            extensions: [".js", ".jsx", ".json", ".vue", ".scss"],
        },
        css: {
            postcss: {
                plugins: [
                    postCssPxToRem({
                        rootValue: 100, // 设计稿尺寸 1rem大小
                        propList: ["*"], // 需要转换的属性，这里选择全部都进行转换
                    }),
                ],
            },
        },
    };
});
console.log([path.resolve(__dirname, "./src/public/utils/hook.**")]);
