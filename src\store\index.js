// export const useAppStore = defineStore("app", () => {
//     const ready = ref(false);
//     return {
//         ready,
//         setReady(bol) {
//             ready.value = bol;
//         },
//     };
// });

import { createStore } from "vuex";

export default createStore({
    state: {
        updateTime: {
            active: 0,
        },
    },
    mutations: {
        setUpdate(state, bool) {
            state.updateTime.active++
        }
    },
    actions: {},
    getters: {
        
    },
});
