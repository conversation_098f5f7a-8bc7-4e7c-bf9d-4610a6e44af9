import { parse } from "@babel/parser";
import traverse from "@babel/traverse";
import generate from "@babel/generator";
import * as t from "@babel/types";
import murmurhash from "murmurhash";
import nodePath from "path";

const root = process.cwd();
export default function VitePluginUseState () {
    if (!process.argv.includes("ducker")) {
        return;
    }
    console.log("ducker enabled....");
    let useStateArguments = {};
    return {
        enforce: "post",
        apply: "build",
        name: "vite-plugin-useState",
        transform (code, id) {
            if (!id.endsWith(".js") && !id.endsWith(".vue")) {
                return;
            }

            const ast = parse(code, {
                sourceType: "module",
                plugins: ["jsx"],
            });

            let hasChanged = false;
            traverse.default(ast, {
                CallExpression (path) {
                    if (path.node.callee.name === "useState") {
                        const argument = path.node.arguments[0];
                        if (argument) {
                            const str = argumentToString(argument);
                            const hashId = `${nodePath.relative(root, id)}(${murmurhash
                                .v3(str)
                                .toString()})`;
                            try {
                                useStateArguments[hashId] = JSON.parse(str);
                            } catch (e) {
                                useStateArguments[hashId] = str;
                            }
                            const windowMember = t.memberExpression(
                                t.identifier("window"),
                                t.identifier("__useStateData"),
                            );

                            path.node.arguments[0] = t.memberExpression(
                                windowMember,
                                t.stringLiteral(hashId),
                                true,
                            );
                            hasChanged = true;
                        }
                    }
                },
            });

            if (hasChanged) {
                const output = generate.default(ast, {}, code);
                return {
                    code: output.code,
                    map: output.map,
                };
            }

            return null;
        },
        configResolved (config) {
            useStateArguments = {};
        },
        transformIndexHtml (html) {
            const scripts = `
<script src="/ducker/configDataOrigin.js"></script>
<script src="/config/configDataModify.js"></script>
            `;
            return html.replace("</title>", `</title>${scripts}`);
        },
        buildEnd () {
            if (Object.values(useStateArguments).length > 0) {
                this.emitFile({
                    type: "asset",
                    fileName: "ducker/configDataOrigin.js",
                    source: `window.__useStateData = ${JSON.stringify(useStateArguments, null, 4)}`,
                });
                this.emitFile({
                    type: "asset",
                    fileName: "config/configDataModify.js",
                    source: `
window.__useStateData = Object.assign(window.__useStateData, {
    //修改的数据写在这里，原始数据在 configDataOrigin.js（不要修改它）
    // "src\\\\views\\\\Main\\\\index.vue(502805669)": {
    //     name: "Main11111",
    // },
});
                    `,
                });
            }
        },
    };
}

function argumentToString (argument) {
    if (t.isStringLiteral(argument)) {
        return argument.value;
    }
    if (t.isNumericLiteral(argument)) {
        return argument.value.toString();
    }
    if (t.isBooleanLiteral(argument)) {
        return argument.value.toString();
    }
    if (t.isIdentifier(argument)) {
        return argument.name;
    }
    if (t.isObjectExpression(argument)) {
        return JSON.stringify(
            argument.properties.reduce((obj, prop) => {
                if (t.isObjectProperty(prop)) {
                    const key = t.isIdentifier(prop.key) ? prop.key.name : prop.key.value;
                    const value = argumentToString(prop.value);
                    obj[key] = value;
                }
                return obj;
            }, {}),
        );
    }
    if (t.isArrayExpression(argument)) {
        return JSON.stringify(argument.elements.map((element) => argumentToString(element)));
    }
    return generate.default(argument).code; // fallback to source code string
}
