/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-02-25 11:35:29
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-05-06 15:04:57
 * @FilePath: \evacuation-simulation-backend\src\public\client\instance\file.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from "axios";
import { AppGlobalConfig } from "@/public/global/const";

const instance = axios.create({
    baseURL: AppGlobalConfig.ServerUrl,
});

// axios.defaults.withCredentials = true;

instance.interceptors.request.use(async (config) => {
    config.responseType = "blob";
    return config;
});

instance.interceptors.response.use((response) => {
    const { data, headers } = response;
    // const fileName = headers["content-disposition"].replace(/\w+;filename=(.*)/, "$1");
    let arr = headers["content-disposition"].split('"');
    let fileName = arr[1];
    console.log(fileName);
    // const fileName = '文件';
    const blob = new Blob([data], { type: headers["content-type"] });
    let dom = document.createElement("a");
    let url = window.URL.createObjectURL(blob);
    dom.href = url;
    dom.download = decodeURI(fileName);
    dom.style.display = "none";
    document.body.appendChild(dom);
    dom.click();
    dom.parentNode.removeChild(dom);
    window.URL.revokeObjectURL(url);
});

export default instance;
