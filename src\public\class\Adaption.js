import {debounce} from "@/public/utils/common";

class Adaption {
    #events = new Set();

    /**
     * width,height 为设计稿的宽高
     * 三种 情况
     * 1. 传 width 根据宽度适配（一般都用不到）
     * 2. 传 height 根据高度适配（常规尺寸大屏使用）
     * 3. 传 width,height 计算宽高最小缩放比，进行适配（超宽屏，适配常规屏使用）
     *
     * 不传参的情况，默认根据 高度为1080进行适配
     */
    constructor({width, height} = {height: 1080}) {
        this.width = width;
        this.height = height;
        this.deviceSize = this.#getDeviceSize();

    }

    #getDeviceSize() {
        return {
            deviceHeight:
                document.documentElement.clientHeight ||
                document.body.clientHeight ||
                document.innerHeight ||
                0,
            deviceWidth:
                document.documentElement.clientWidth ||
                document.body.clientWidth ||
                document.innerWidth ||
                0,
        };
    }

    #setDocFontSize() {
        document.documentElement.style.fontSize = this.fontSize(100) + "px";
    }

    add(callback) {
        this.#events.add(callback);
    }

    delete(callback) {
        this.#events.delete(callback);
    }

    init() {
        window.addEventListener(
            "resize",
            debounce(() => {
                this.deviceSize = this.#getDeviceSize();
                this.#setDocFontSize();
                this.#events.forEach((callback) => {
                    callback();
                });
            }, 300),
        );
        this.#setDocFontSize();
    }

    fontSize(size) {
        const {deviceWidth, deviceHeight} = this.deviceSize;
        let widthScale, heightScale;
        if (this.width) {
            widthScale = deviceWidth / this.width;
        }
        if (this.height) {
            heightScale = deviceHeight / this.height;
        }
        return size * Math.min(...[widthScale, heightScale].filter(Boolean));
    }
}

export default Adaption;
