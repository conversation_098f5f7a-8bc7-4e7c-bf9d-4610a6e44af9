<!--
 * @Author: hkh <EMAIL>
 * @Date: 2023-04-29 19:16:27
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2023-05-03 10:28:39
 * @FilePath: \template\src\component\ECharts\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div ref="chartdom"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { watch, onMounted, defineProps, onBeforeUnmount, ref, defineEmits } from "vue";
let chart = ref(null);
//echarts 实例赋值给 reactive 响应式 Proxy对象 会导致不显示 tootip
//拆箱方法
const unwarp = (obj) => obj && (obj.__v_raw || obj.valueOf() || obj);

const props = defineProps({
  options: Object,
  theme: String,
});
const timer = ref(null);
const chartdom = ref(null);
onMounted(() => {
  $_init();
  window.addEventListener("resize", resize, false);
});
const emit = defineEmits(["charteEvent"]);

const $_init = () => {
  chart.value = echarts.init(chartdom.value, props.theme, { devicePixelRatio: 2 });
  emit("charteEvent", chart.value);
};
const $_setOption = () => {
  if (props.options && chart.value) {
    unwarp(chart.value).setOption(props.options, true);
  }
};
const resize = () => {
  clearTimeout(timer.value);
  timer.value = setTimeout(() => {
    chart.value.resize();
  }, 300);
};
watch(
  () => props.options,
  () => {
    $_setOption();
  },
  // 可选项，是否开启深监听
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => chart.value,
  () => {
    $_setOption();
  }
);

onBeforeUnmount(() => {
  unwarp(chart.value)?.dispose();
  chart.value = null;
  window.removeEventListener("resize", resize);
});
</script>

<style scoped></style>
