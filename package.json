{"name": "call-me-ducker", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "ducker": "vite build -- ducker", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.11.0", "axios": "^1.7.2", "dayjs": "^1.11.11", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "element-plus": "^2.7.7", "highcharts": "^10.2.1", "moment": "^2.30.1", "pinia": "^2.1.7", "ramda": "^0.30.1", "vue": "^3.4.29", "vue-router": "^4.4.0", "vuex": "^4.1.0"}, "devDependencies": {"@babel/eslint-parser": "^7.18.2", "@babel/generator": "^7.24.8", "@babel/parser": "^7.24.8", "@babel/traverse": "^7.24.8", "@babel/types": "^7.24.8", "@vitejs/plugin-vue": "^5.0.5", "eslint": "8.21.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.27.0", "murmurhash": "^2.0.1", "postcss-pxtorem": "^6.1.0", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.4.1", "sass": "^1.77.7", "unocss": "^0.61.3", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.27.2", "vite": "^5.3.1", "vite-plugin-html": "^3.2.2"}}