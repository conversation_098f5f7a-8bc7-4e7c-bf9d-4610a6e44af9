export default {
    bind(el, binding, vnode) {
      el.draggable = true;
  
      el.addEventListener('dragstart', (event) => {
        event.dataTransfer.setData('text/plain', binding.value);
        el.classList.add('dragging');
      });
  
      el.addEventListener('dragend', () => {
        el.classList.remove('dragging');
      });
  
      el.addEventListener('dragover', (event) => {
        event.preventDefault();
      });
  
      el.addEventListener('drop', (event) => {
        event.preventDefault();
        const draggedIndex = event.dataTransfer.getData('text/plain');
        const droppedIndex = binding.value;
  
        console.log('Drag Drop Event: ', { draggedIndex, droppedIndex });
        vnode.context.handleDrop({ draggedIndex, droppedIndex });
      });
    }
  };