<template>
    <div ref="dom"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { useEffect } from "@/public/utils/hook.js";

const props = defineProps({
    options: Object,
});
const dom = ref();
const chart = shallowRef();
const adaption = inject("adaption");
const emits = defineEmits(["init"]);
onMounted(() => {
    chart.value = echarts.init(dom.value);
    adaption.add(() => {
        chart.value.resize();
    });
    emits("init", chart.value);
});
useEffect(() => {
    if (chart.value) {
        chart.value.setOption(props.options);
    }
}, [chart, toRef(props, "options")]);

defineExpose({ chart });
</script>

<style lang="scss" scoped></style>
