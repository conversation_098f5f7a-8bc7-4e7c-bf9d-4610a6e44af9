<template>
    <div class="right">
        <BaseTitle />
        <div class="body">
            <div class="title">{{ data.title || "预案详情" }}</div>
            <div class="form-box">
                <div class="form-box-item">
                    <div class="form-title">预案适用条件</div>
                    <div class="form-content">
                        <div class="form-item">
                            <div class="form-title">事件等级</div>
                            <el-input type="text" :value="data.level" disabled />
                        </div>
                        <div class="form-item">
                            <div class="form-title">演练地点</div>
                            <el-input type="text" :value="data.addr" disabled />
                        </div>
                        <div class="form-item">
                            <div class="form-title">疏散人数</div>
                            <el-input type="text" :value="data.ssrs" disabled />
                        </div>
                        <div class="text-item">
                            <div class="text-title">投放详情：</div>
                            <div class="text-info">
                                {{ tfText || "其中A区域2354人，B区域25人，C区域25人，D区域25人" }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-box-item">
                    <div class="form-title">设备控制</div>
                    <div class="form-content">
                        <div class="table-item">
                            <div class="table-title">出口配置：</div>
                            <div class="table-info">
                                <el-table :data="ckTableData" border>
                                    <el-table-column prop="id" label="序号"></el-table-column>
                                    <el-table-column prop="name" label="序号"></el-table-column>
                                    <el-table-column prop="addr" label="所属区域"></el-table-column>
                                    <el-table-column label="开关状态">
                                        <template #default="scope">
                                            <el-switch v-model="scope.row.status" disabled />
                                            <!-- @change="chageStatus(scope.row)" -->
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                        <!-- <div class="table-item">
                            <div class="table-title">门禁配置：</div>
                            <div class="table-info">
                                <el-table :data="ckTableData" border>
                                    <el-table-column prop="id" label="序号"></el-table-column>
                                    <el-table-column prop="name" label="序号"></el-table-column>
                                    <el-table-column prop="addr" label="所属区域"></el-table-column>
                                    <el-table-column
                                        prop="status"
                                        label="开关状态"
                                    ></el-table-column>
                                </el-table>
                            </div>
                        </div> -->
                        <div class="table-item">
                            <div class="table-title">道闸配置：</div>
                            <div class="table-info">
                                <el-table :data="dzTableData" border>
                                    <el-table-column prop="id" label="序号"></el-table-column>
                                    <el-table-column prop="name" label="序号"></el-table-column>
                                    <el-table-column prop="addr" label="所属区域"></el-table-column>
                                    <el-table-column prop="status" label="开关状态">
                                        <template #default="scope">
                                            <el-switch v-model="scope.row.status" disabled />
                                            <!-- @change="chageStatus(scope.row)" -->
                                        </template></el-table-column
                                    >
                                </el-table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import BaseTitle from "@/components/BaseTitle/index.vue";
import { YjyaDetail } from "@/apis";
import dayjs from "dayjs";

const router = useRouter();
const route = useRoute();

onMounted(() => {
    getDetail();
});

const data = ref({
    level: null,
    addr: null,
    ssrs: null,
    
});
const tfText = ref("");
const ckTableData = ref([
    {
        id: 1,
        name: "c1",
        addr: "c1",
        status: true,
    },
]);
const dzTableData = ref([]);
const getDetail = () => {
    YjyaDetail({
        id: route.query.id,
    }).then((res) => {
        data.value = res.data;
        console.log(1);
        try {
            //疏散人员
            let params = JSON.parse(res.data.rypz);
            let text = "其中";
            let num = 0;
            params.datas.forEach((item) => {
                let name = item.areaName;
                if (name.indexOf("区") === -1) {
                    name = name + "区域";
                }
                text += `${name}${item.manNum + item.womanNum}人，`;
                num += item.manNum + item.womanNum;
            });
            tfText.value = text;
        } catch (error) {}
        try {
            let arr = JSON.parse(res.data.crkpz);
            ckTableData.value = arr.datas.map((item, index) => {
                return {
                    id: 1 + index,
                    name: item.name,
                    addr: "体育馆",
                    status: item.isCanConfig,
                };
            });
        } catch (error) {}
        try {
            let arr = JSON.parse(res.data.dzpz);
            dzTableData.value = arr.datas.map((item, index) => {
                return {
                    id: 1 + index,
                    name: item.name,
                    addr: "体育馆",
                    status: item.isCanConfig,
                };
            });
        } catch (error) {}
    });
};

const chageStatus = (item) => {
    console.log(item);
};
</script>

<style scoped lang="scss">
.right {
    width: calc(100% - 336px);
}
.body {
    padding: 50px 50px;
    height: calc(100% - 122px);
    position: relative;
    .title {
        margin-bottom: 50px;
        font-size: 20px;
        font-weight: bold;
    }
}
.form-box {
    width: 100%;
    height: calc(100% - 60px);
    background: #ffffff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;
    padding: 50px;
    overflow-y: scroll;
    .form-box-item {
        margin-bottom: 50px;
        & > .form-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .form-content {
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            .form-item {
                width: 35%;
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                margin-right: 20px;
                .form-title {
                    width: 100px;
                    font-size: 16px;
                    font-weight: bold;
                }
            }
            .text-item {
                width: 100%;
                display: flex;
                .text-title {
                    width: 80px;
                }
                .text-info {
                    width: calc(100% - 80px);
                }
            }
            .table-item {
                width: 100%;
                margin-bottom: 20px;
                .table-title {
                    font-size: 15px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
                .table-info {
                    width: 600px;
                }
            }
            .chart-item {
                width: 33%;
                margin-bottom: 20px;
                border-radius: 20px 20px 20px 20px;
                border: 1px solid rgba(0, 0, 0, 0.06);
                height: 190px;
                padding: 24px;
                &:first-child {
                    margin-right: 4%;
                }
                &.chart-big {
                    width: 70%;
                }
            }
        }
    }
}
</style>
