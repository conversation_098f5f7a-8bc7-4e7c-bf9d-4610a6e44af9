<template>
    <div class="right">
        <BaseTitle />
        <div class="body">
            <div class="title">应急事件</div>
            <div class="search-box">
                <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                    <el-form-item label="所属系统">
                        <el-select v-model="searchForm.systemCode" placeholder="请选择" clearable>
                            <el-option
                                v-for="system in systemDictionary"
                                :key="system.code"
                                :label="system.name"
                                :value="system.code"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="处理状态">
                        <el-select v-model="searchForm.deal" placeholder="请选择" clearable>
                            <el-option label="未处理" :value="0" />
                            <el-option label="处理中" :value="1" />
                            <el-option label="已处理" :value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">查询</el-button>
                        <el-button @click="onReset">重置</el-button>
                    </el-form-item>
                </el-form>
                <div class="add-btn">
                    <!-- <el-button type="primary" @click="handleAdd">新增事件</el-button> -->
                </div>
            </div>
            <div class="table-box">
                <el-table
                    ref="multipleTableRef"
                    :data="tableData"
                    row-key="id"
                    style="width: 100%; height: 451px"
                    :loading="tableLoading"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column property="id" label="序号" width="200" />
                    <el-table-column property="ruleName" label="事件名称" min-width="150" />
                    <el-table-column property="level" label="事件等级" width="100">
                        <template #default="scope">
                            <el-tag
                                :type="
                                    scope.row.level === 4
                                        ? 'danger'
                                        : scope.row.level === 3
                                        ? 'warning'
                                        : scope.row.level === 2
                                        ? 'info'
                                        : 'success'
                                "
                            >
                                {{ getLevelDesc(scope.row.level) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column property="systemName" label="所属系统" width="120" />
                    <el-table-column property="createDate" label="发生时间" width="180">
                        <template #default="scope">
                            {{ formatTime(scope.row.createDate) }}
                        </template>
                    </el-table-column>
                    <el-table-column property="deviceAddress" label="事件地点" width="300" />
                    <el-table-column property="deal" label="事件状态" width="100">
                        <template #default="scope">
                            <el-tag
                                :type="
                                    scope.row.deal === 2
                                        ? 'success'
                                        : scope.row.deal === 1
                                        ? 'warning'
                                        : 'info'
                                "
                            >
                                {{ getDealDesc(scope.row.deal) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template #default="scope">
                            <el-button text @click="handleView(scope.row)">详情</el-button>
                            <!-- <el-button text @click="handleEdit(scope.row)">编辑</el-button>
                            <el-popconfirm title="确认删除?" @confirm="handleDelete(scope.row)">
                                <template #reference>
                                    <el-button text>删除</el-button>
                                </template>
                            </el-popconfirm> -->
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50]"
                    :disabled="disabled"
                    :background="background"
                    layout=" prev, pager, next, total, sizes, jumper"
                    :total="total"
                    class="page-box"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>
    </div>

    <!-- 新增/编辑应急事件弹窗 -->
    <el-dialog v-model="eventDialogVisible" :title="dialogTitle" width="800px" class="event-dialog">
        <div class="dialog-content">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                <el-tab-pane label="事件基本信息" name="basic">
                    <el-form
                        :model="eventForm"
                        ref="eventFormRef"
                        label-width="120px"
                        class="event-form"
                    >
                        <div class="form-row">
                            <el-form-item label="事件名称" prop="ruleName" required>
                                <el-input
                                    v-model="eventForm.ruleName"
                                    placeholder="请输入事件名称"
                                    size="large"
                                    :disabled="isView"
                                />
                            </el-form-item>
                            <el-form-item label="设备编码" prop="deviceCode" required>
                                <el-input
                                    v-model="eventForm.deviceCode"
                                    placeholder="请输入设备编码"
                                    size="large"
                                    :disabled="isView"
                                />
                            </el-form-item>
                        </div>
                        <div class="form-row">
                            <el-form-item label="所属系统" prop="systemCode" required>
                                <el-select
                                    v-model="eventForm.systemCode"
                                    placeholder="请选择所属系统"
                                    style="width: 100%"
                                    size="large"
                                    :disabled="isView"
                                    @change="handleSystemChange"
                                >
                                    <el-option
                                        v-for="system in systemDictionary"
                                        :key="system.code"
                                        :label="system.name"
                                        :value="system.code"
                                    />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="事件等级" prop="level" required>
                                <el-select
                                    v-model="eventForm.level"
                                    placeholder="请选择事件等级"
                                    style="width: 100%"
                                    size="large"
                                    :disabled="isView"
                                >
                                    <el-option label="提醒" :value="1" />
                                    <el-option label="一般" :value="2" />
                                    <el-option label="严重" :value="3" />
                                    <el-option label="紧急" :value="4" />
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="form-row">
                            <el-form-item label="发生时间" prop="createDate" required>
                                <el-date-picker
                                    v-model="eventForm.createDate"
                                    type="datetime"
                                    placeholder="请选择发生时间"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    value-format="x"
                                    style="width: 100%"
                                    size="large"
                                    :disabled="isView"
                                />
                            </el-form-item>
                            <el-form-item label="设备地址" prop="deviceAddress" required>
                                <el-input
                                    v-model="eventForm.deviceAddress"
                                    placeholder="请输入设备地址"
                                    size="large"
                                    :disabled="isView"
                                />
                            </el-form-item>
                        </div>
                        <div class="form-row">
                            <el-form-item label="设备名称" prop="deviceName" required>
                                <el-input
                                    v-model="eventForm.deviceName"
                                    placeholder="请输入设备名称"
                                    size="large"
                                    :disabled="isView"
                                />
                            </el-form-item>
                            <el-form-item label="上报值" prop="reportedVal">
                                <el-input
                                    v-model="eventForm.reportedVal"
                                    placeholder="请输入上报值"
                                    size="large"
                                    :disabled="isView"
                                />
                            </el-form-item>
                        </div>
                        <div class="form-row">
                            <el-form-item label="设备属性" prop="alarmAttr">
                                <el-input
                                    v-model="eventForm.alarmAttr"
                                    placeholder="请输入设备属性"
                                    size="large"
                                    :disabled="isView"
                                />
                            </el-form-item>
                            <el-form-item label="事件状态" prop="deal" required>
                                <el-select
                                    v-model="eventForm.deal"
                                    placeholder="请选择事件状态"
                                    style="width: 100%"
                                    size="large"
                                    :disabled="isView"
                                >
                                    <el-option label="未处理" :value="0" />
                                    <el-option label="处理中" :value="1" />
                                    <el-option label="已处理" :value="2" />
                                </el-select>
                            </el-form-item>
                        </div>
                        <el-form-item label="告警原因" prop="threshold" required>
                            <el-input
                                v-model="eventForm.threshold"
                                type="textarea"
                                :rows="3"
                                placeholder="请输入告警原因..."
                                resize="none"
                                :disabled="isView"
                            />
                        </el-form-item>
                        <el-form-item label="告警说明" prop="remark">
                            <el-input
                                v-model="eventForm.remark"
                                type="textarea"
                                :rows="3"
                                placeholder="请输入告警说明..."
                                resize="none"
                                :disabled="isView"
                            />
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="事件进展" name="progress" v-if="isView">
                    <div class="progress-box">
                        <div class="progress-header">
                            <div class="progress-tabs">
                                <div
                                    class="progress-tab"
                                    @click="changeProgressTab('全部进展')"
                                    :class="{ active: activeProgressTab === '全部进展' }"
                                >
                                    全部进展
                                </div>
                                <!-- <div
                                    class="progress-tab"
                                    @click="changeProgressTab('指令调度')"
                                    :class="{ active: activeProgressTab === '指令调度' }"
                                >
                                    指令调度
                                </div> -->
                            </div>
                            <el-button type="primary" size="small" @click="handleAddProgress">
                                新增进展
                            </el-button>
                        </div>
                        <div class="timeline-container">
                            <div
                                class="timeline-item"
                                v-for="(item, index) in filteredTimelineData"
                                :key="item.id || index"
                            >
                                <div
                                    class="timeline-dot"
                                    :class="getStatusClass(item.taskStatus)"
                                ></div>
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-time">
                                            {{ item.progressTime }}
                                        </div>
                                        <div class="timeline-actions">
                                            <el-button
                                                type="text"
                                                size="small"
                                                @click="handleEditProgress(item)"
                                            >
                                                编辑
                                            </el-button>
                                            <el-popconfirm
                                                title="确认删除此进展记录?"
                                                @confirm="handleDeleteProgress(item)"
                                            >
                                                <template #reference>
                                                    <el-button
                                                        type="text"
                                                        size="small"
                                                        style="color: #f56c6c"
                                                    >
                                                        删除
                                                    </el-button>
                                                </template>
                                            </el-popconfirm>
                                        </div>
                                    </div>
                                    <div class="timeline-title">
                                        【{{ item.progressStage }}】{{ item.taskContent }}
                                        <el-tag
                                            size="small"
                                            :type="getStatusTagType(item.taskStatus)"
                                            style="margin-left: 8px"
                                        >
                                            {{ item.taskStatus }}
                                        </el-tag>
                                        <el-tag
                                            size="small"
                                            :type="
                                                item.priorityLevel === '紧急'
                                                    ? 'danger'
                                                    : item.priorityLevel === '高'
                                                    ? 'warning'
                                                    : 'info'
                                            "
                                            style="margin-left: 4px"
                                        >
                                            {{ item.priorityLevel }}
                                        </el-tag>
                                    </div>
                                    <div class="timeline-desc" v-if="item.remarks">
                                        {{ item.remarks }}
                                    </div>
                                    <div
                                        class="timeline-progress"
                                        v-if="item.completionRate !== undefined"
                                    >
                                        <div class="progress-label">
                                            完成进度：{{ item.completionRate }}%
                                        </div>
                                        <el-progress
                                            :percentage="item.completionRate"
                                            :stroke-width="6"
                                            :color="
                                                item.completionRate === 100 ? '#67c23a' : '#409eff'
                                            "
                                        />
                                    </div>
                                    <div class="timeline-meta">
                                        <div class="timeline-contact" v-if="item.taskReceiver">
                                            负责人：{{ item.taskReceiver }}
                                        </div>
                                        <div class="timeline-contact" v-if="item.contactPhone">
                                            联系方式：{{ item.contactPhone }}
                                        </div>
                                        <div
                                            class="timeline-contact"
                                            v-if="item.expectedCompletionTime"
                                        >
                                            预期完成：{{ item.expectedCompletionTime }}
                                        </div>
                                        <div
                                            class="timeline-contact"
                                            v-if="item.actualCompletionTime"
                                        >
                                            实际完成：{{ item.actualCompletionTime }}
                                        </div>
                                        <div class="timeline-contact" v-if="item.attachmentUrl">
                                            附件：
                                            <el-button
                                                type="primary"
                                                text
                                                size="small"
                                                @click="downloadProgressFile(item.attachmentUrl)"
                                            >
                                                {{ getFileName(item.attachmentUrl) }}
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button size="large" @click="eventDialogVisible = false">{{
                    isView ? "关闭" : "取消"
                }}</el-button>
                <el-button type="primary" size="large" @click="handleSaveEvent" v-if="!isView"
                    >保存</el-button
                >
            </div>
        </template>
    </el-dialog>

    <!-- 新增/编辑进展弹窗 -->
    <el-dialog
        v-model="progressDialogVisible"
        :title="progressDialogTitle"
        width="700px"
        :close-on-click-modal="false"
        class="progress-dialog"
    >
        <div class="progress-dialog-content">
            <el-form
                :model="progressForm"
                label-width="120px"
                :rules="progressRules"
                ref="progressFormRef"
                class="progress-form"
            >
                <div class="form-section">
                    <div class="section-title">基本信息</div>
                    <div class="form-row">
                        <el-form-item label="进展阶段" prop="progressStage" class="form-item-half">
                            <el-select
                                v-model="progressForm.progressStage"
                                placeholder="请选择进展阶段"
                                style="width: 100%"
                                size="large"
                            >
                                <el-option label="初期响应" value="初期响应" />
                                <el-option label="人员疏散" value="人员疏散" />
                                <el-option label="救援响应" value="救援响应" />
                                <el-option label="现场处置" value="现场处置" />
                                <el-option label="后续处理" value="后续处理" />
                                <el-option label="事件总结" value="事件总结" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="任务状态" prop="taskStatus" class="form-item-half">
                            <el-select
                                v-model="progressForm.taskStatus"
                                placeholder="请选择任务状态"
                                style="width: 100%"
                                size="large"
                            >
                                <el-option label="待开始" value="待开始">
                                    <span class="status-option">
                                        <span class="status-dot pending"></span>
                                        待开始
                                    </span>
                                </el-option>
                                <el-option label="进行中" value="进行中">
                                    <span class="status-option">
                                        <span class="status-dot processing"></span>
                                        进行中
                                    </span>
                                </el-option>
                                <el-option label="暂停" value="暂停">
                                    <span class="status-option">
                                        <span class="status-dot paused"></span>
                                        暂停
                                    </span>
                                </el-option>
                                <el-option label="已完成" value="已完成">
                                    <span class="status-option">
                                        <span class="status-dot completed"></span>
                                        已完成
                                    </span>
                                </el-option>
                                <el-option label="已取消" value="已取消">
                                    <span class="status-option">
                                        <span class="status-dot cancelled"></span>
                                        已取消
                                    </span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>
                    <div class="form-row">
                        <el-form-item label="优先级" prop="priorityLevel" class="form-item-half">
                            <el-select
                                v-model="progressForm.priorityLevel"
                                placeholder="请选择优先级"
                                style="width: 100%"
                                size="large"
                            >
                                <el-option label="低" value="低" />
                                <el-option label="一般" value="一般" />
                                <el-option label="高" value="高" />
                                <el-option label="紧急" value="紧急" />
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            label="完成进度(%)"
                            prop="completionRate"
                            class="form-item-half"
                        >
                            <el-slider
                                v-model="progressForm.completionRate"
                                :min="0"
                                :max="100"
                                :step="10"
                                show-input
                                :format-tooltip="(val) => `${val}%`"
                            />
                        </el-form-item>
                    </div>
                    <el-form-item label="任务内容" prop="taskContent">
                        <el-input
                            v-model="progressForm.taskContent"
                            placeholder="请输入任务内容"
                            size="large"
                        />
                    </el-form-item>
                    <div class="form-row">
                        <el-form-item label="任务接收人" prop="taskReceiver" class="form-item-half">
                            <el-input
                                v-model="progressForm.taskReceiver"
                                placeholder="请输入任务接收人"
                                size="large"
                            />
                        </el-form-item>
                        <el-form-item label="联系方式" prop="contactPhone" class="form-item-half">
                            <el-input
                                v-model="progressForm.contactPhone"
                                placeholder="请输入联系方式"
                                size="large"
                            />
                        </el-form-item>
                    </div>
                    <el-form-item label="进展发生时间" prop="progressTime">
                        <el-date-picker
                            v-model="progressForm.progressTime"
                            type="datetime"
                            placeholder="请选择进展发生时间"
                            format="YYYY-MM-DD HH:mm:ss"
                            value-format="YYYY-MM-DD HH:mm:ss"
                            style="width: 100%"
                            size="large"
                        />
                    </el-form-item>
                </div>

                <div class="form-section">
                    <div class="section-title">时间计划</div>
                    <div class="form-row">
                        <el-form-item
                            label="预期完成时间"
                            prop="expectedCompletionTime"
                            class="form-item-half"
                        >
                            <el-date-picker
                                v-model="progressForm.expectedCompletionTime"
                                type="datetime"
                                placeholder="请选择预期完成时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                style="width: 100%"
                                size="large"
                            />
                        </el-form-item>
                        <el-form-item
                            label="实际完成时间"
                            prop="actualCompletionTime"
                            class="form-item-half"
                        >
                            <el-date-picker
                                v-model="progressForm.actualCompletionTime"
                                type="datetime"
                                placeholder="请选择实际完成时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                style="width: 100%"
                                size="large"
                            />
                        </el-form-item>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">详细信息</div>
                    <el-form-item label="备注信息">
                        <el-input
                            v-model="progressForm.remarks"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入备注信息..."
                            resize="none"
                        />
                    </el-form-item>
                    <el-form-item label="附件上传">
                        <div class="attachment-container">
                            <el-upload
                                v-if="!progressForm.attachmentUrl"
                                drag
                                action=""
                                :before-upload="uploadProgressFile"
                                :show-file-list="false"
                            >
                                <el-icon class="el-icon--upload">
                                    <UploadFilled />
                                </el-icon>
                                <div class="el-upload__text">
                                    将文件拖到此处，或<em>点击上传</em>
                                </div>
                            </el-upload>
                            <div v-else class="uploaded-file">
                                <div class="file-info">
                                    <el-icon><UploadFilled /></el-icon>
                                    <span class="file-name">{{
                                        getFileName(progressForm.attachmentUrl)
                                    }}</span>
                                </div>
                                <div class="file-actions">
                                    <el-button
                                        type="primary"
                                        size="small"
                                        @click="downloadProgressFile(progressForm.attachmentUrl)"
                                    >
                                        下载
                                    </el-button>
                                    <el-button
                                        type="danger"
                                        size="small"
                                        @click="removeProgressFile"
                                    >
                                        删除
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </el-form>
        </div>

        <template #footer>
            <div class="progress-dialog-footer">
                <el-button size="large" @click="progressDialogVisible = false">取消</el-button>
                <el-button type="primary" size="large" @click="handleSaveProgress">
                    {{ progressDialogMode === "add" ? "创建进展" : "保存修改" }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, computed, inject } from "vue";
import { useRouter, useRoute } from "vue-router";
import BaseTitle from "@/components/BaseTitle/index.vue";
import { ElMessage } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import {
    ProgressList,
    ProgressSave,
    ProgressDelete,
    alarmDesc,
    bupload,
    DrillDownload,
} from "@/apis/index.js";

const router = useRouter();
const route = useRoute();

const multipleTableRef = ref("");
const multipleSelection = ref([]);
const eventFormRef = ref(null);
const progressFormRef = ref(null);

// 获取全局字典数据
const dictData = inject("dictData");

// 所属系统字典数据
const systemDictionary = computed(() => {
    return dictData.value.map((item) => ({
        name: item.dictValue,
        code: item.dictCode,
    }));
});

// 弹窗状态
const eventDialogVisible = ref(false);
const progressDialogVisible = ref(false);
const activeTab = ref("basic");
const activeProgressTab = ref("全部进展");
const dialogMode = ref("add"); // add, edit, view
const progressDialogMode = ref("add"); // add, edit

const isView = computed(() => dialogMode.value === "view");
const dialogTitle = computed(() => {
    switch (dialogMode.value) {
        case "add":
            return "新增应急事件";
        case "edit":
            return "编辑应急事件";
        case "view":
            return "应急事件详情";
        default:
            return "应急事件";
    }
});

const progressDialogTitle = computed(() => {
    return progressDialogMode.value === "add" ? "新增进展" : "编辑进展";
});
const tableLoading = ref(false);
const searchForm = reactive({
    ruleName: "",
    systemCode: "",
    deal: undefined,
});
// 事件表单数据 - 使用111.text中的字段结构
const eventForm = reactive({
    id: "",
    alarmAttr: "",
    ruleName: "",
    threshold: "",
    reportedVal: "",
    createDate: null,
    resetDate: null,
    deal: 0,
    deviceAddress: "",
    deviceCode: "",
    deviceName: "",
    level: 1,
    levelDesc: "",
    alarmTag: "",
    alarmReasonStr: "",
    remark: "",
    productName: "",
    productCode: "",
    systemCode: "",
    systemName: "",
});

// 进展表单数据 - 使用111.text中的进展字段结构
const progressForm = reactive({
    id: null, // 进展ID（新增时不传，更新时必传）
    eventId: null, // 关联的事件ID
    progressStage: "", // 进展阶段
    taskContent: "", // 任务内容
    taskReceiver: "", // 任务接收人
    contactPhone: "", // 联系方式
    progressTime: null, // 进展发生时间
    taskStatus: "待开始", // 任务状态（默认：待开始）
    completionRate: 0, // 完成进度0-100（默认：0）
    priorityLevel: "一般", // 优先级（默认：一般）
    expectedCompletionTime: null, // 预期完成时间
    actualCompletionTime: null, // 实际完成时间
    remarks: "", // 备注
    attachmentUrl: "", // 附件地址
    progressType: "全部进展", // 进展类型分类
});

// 进展表单验证规则
const progressRules = {
    progressStage: [{ required: true, message: "请选择进展阶段", trigger: "change" }],
    taskContent: [{ required: true, message: "请输入任务内容", trigger: "blur" }],
    taskReceiver: [{ required: true, message: "请输入任务接收人", trigger: "blur" }],
    progressTime: [{ required: true, message: "请选择进展发生时间", trigger: "change" }],
};

// 照片数据
const photos = ref([
    "/src/assets/vector.png",
    "/src/assets/vector.png",
    "/src/assets/vector.png",
    "/src/assets/vector.png",
    "/src/assets/vector.png",
    "/src/assets/vector.png",
]);

// 时间线数据 - 使用111.text中的进展字段结构
const timelineData = ref([
    {
        id: 1,
        eventId: null,
        progressStage: "初期响应",
        taskContent: "消防系统自动响应",
        taskReceiver: "王明峰",
        contactPhone: "15623451245",
        progressTime: "2024-12-03 12:00:10",
        taskStatus: "已完成",
        completionRate: 100,
        priorityLevel: "紧急",
        expectedCompletionTime: "2024-12-03 12:05:00",
        actualCompletionTime: "2024-12-03 12:02:30",
        remarks: "体育中心F1-D消防系统检测到烟雾，自动启动消防设备",
        attachmentUrl: "",
        progressType: "全部进展",
    },
    {
        id: 2,
        eventId: null,
        progressStage: "人员疏散",
        taskContent: "现场人员疏散指令",
        taskReceiver: "李明华",
        contactPhone: "13856742131",
        progressTime: "2024-12-03 12:01:00",
        taskStatus: "进行中",
        completionRate: 60,
        priorityLevel: "紧急",
        expectedCompletionTime: "2024-12-03 12:15:00",
        actualCompletionTime: null,
        remarks: "启动人员疏散预案，组织现场人员有序撤离到安全区域",
        attachmentUrl: "",
        progressType: "指令调度",
    },
    {
        id: 3,
        eventId: null,
        progressStage: "救援响应",
        taskContent: "消防队伍到达现场",
        taskReceiver: "赵强",
        contactPhone: "18912345678",
        progressTime: "2024-12-03 12:03:00",
        taskStatus: "已完成",
        completionRate: 100,
        priorityLevel: "紧急",
        expectedCompletionTime: "2024-12-03 12:20:00",
        actualCompletionTime: "2024-12-03 12:08:50",
        remarks: "专业消防队伍到达现场，开始灭火救援工作",
        attachmentUrl: "",
        progressType: "全部进展",
    },
]);

// 表格数据 - 使用新的字段结构
const tableData = ref([
    {
        id: "1",
        ruleName: "消防系统火灾告警",
        level: 4,
        systemCode: "belong_system_fire_control",
        systemName: "消防系统",
        createDate: 1696161610000,
        deviceAddress: "体育中心F1-D区域",
        deal: 2,
        deviceCode: "XFXT_001",
        deviceName: "烟感探测器",
        threshold: "烟雾浓度超过阈值",
    },
    {
        id: "2",
        ruleName: "环境系统温度异常",
        level: 3,
        systemCode: "belong_system_environment",
        systemName: "环境系统",
        createDate: 1696161610000,
        deviceAddress: "体育中心B1区域",
        deal: 1,
        deviceCode: "HJXT_002",
        deviceName: "温度传感器",
        threshold: "温度超过设定范围",
    },
    {
        id: "3",
        ruleName: "配电系统电压异常",
        level: 2,
        systemCode: "belong_system_power_protection",
        systemName: "配电系统",
        createDate: 1696161610000,
        deviceAddress: "体育中心配电房",
        deal: 0,
        deviceCode: "PDXT_003",
        deviceName: "电压监测仪",
        threshold: "电压波动超出正常范围",
    },
    {
        id: "4",
        ruleName: "门禁系统异常开启",
        level: 1,
        systemCode: "belong_system_door_control",
        systemName: "门禁系统",
        createDate: 1696161610000,
        deviceAddress: "体育中心主入口",
        deal: 1,
        deviceCode: "MJXT_004",
        deviceName: "门禁控制器",
        threshold: "门禁异常开启",
    },
    {
        id: "5",
        ruleName: "给排水系统压力不足",
        level: 3,
        systemCode: "belong_system_water_and_wastewater",
        systemName: "给排水系统",
        createDate: 1696161610000,
        deviceAddress: "体育中心地下室",
        deal: 2,
        deviceCode: "GPSXT_005",
        deviceName: "水压传感器",
        threshold: "水压低于正常值",
    },
]);
let end_time = dayjs().format("YYYY-MM-DD");
let start_time = dayjs().subtract(0, "day").format("YYYY-MM-DD");

const getalarmDesc = async (page = 1, size = 10) => {
    tableLoading.value = true;
    try {
        const res = await alarmDesc({
            model: {
                start_time,
                end_time,
                project_code: "ZH_00052_XM_00000001",
                system_code: searchForm.systemCode || undefined,
                deal: searchForm.deal,
                rule_name: searchForm.ruleName || undefined,
            },
            page: page,
            pageSize: size,
        });
        console.log(res);

        // 更新表格数据
        tableData.value = res.data.list.map((item) => ({
            id: item.id,
            ruleName: item.rule_name,
            level: item.level,
            levelDesc: item.level_desc,
            systemCode: item.system_code,
            systemName: item.system_name,
            createDate: dayjs(item.create_date).valueOf(), // 转换为时间戳格式
            deviceAddress: item.device_address,
            deal: item.deal,
            dealDesc: item.deal_desc,
            deviceCode: item.device_code,
            deviceName: item.device_name,
            threshold: item.threshold,
            reportedVal: item.reported_val,
            alarmReasonStr: item.alarm_desc,
            receiver: item.receiver,
            workOrderId: item.work_order_id,
            // 保留原有字段以兼容现有逻辑
            alarmAttr: item.device_name || "",
            resetDate: null,
            alarmTag: "",
            remark: item.alarm_desc || "",
            productName: "",
            productCode: "",
        }));

        // 更新分页信息
        const pageInfo = res.data.pageInfo;
        total.value = pageInfo.totalCount;
        currentPage.value = pageInfo.currentPage;
        pageSize.value = pageInfo.pageSize;
    } catch (error) {
        console.error("获取告警数据失败：", error);
        ElMessage.error("获取数据失败");
    } finally {
        tableLoading.value = false;
    }
};

// 初始化加载数据
getalarmDesc();

// 工具函数
const getLevelDesc = (level) => {
    const levelMap = {
        1: "提醒",
        2: "一般",
        3: "严重",
        4: "紧急",
    };
    return levelMap[level] || "未知";
};

const getDealDesc = (deal) => {
    const dealMap = {
        0: "未处理",
        1: "处理中",
        2: "已处理",
    };
    return dealMap[deal] || "未知";
};

const formatTime = (timestamp) => {
    if (!timestamp) return "";
    // 如果是字符串格式的时间，直接转换显示格式
    if (typeof timestamp === "string") {
        return dayjs(timestamp).format("YYYY.MM.DD HH:mm:ss");
    }
    return dayjs(timestamp).format("YYYY.MM.DD HH:mm:ss");
};

const handleSystemChange = () => {
    const selectedSystem = systemDictionary.value.find(
        (item) => item.code === eventForm.systemCode,
    );
    if (selectedSystem) {
        eventForm.systemName = selectedSystem.name;
    }
};

// 进展相关的工具函数
const getStatusClass = (taskStatus) => {
    const statusMap = {
        待开始: "pending",
        进行中: "processing",
        暂停: "paused",
        已完成: "completed",
        已取消: "cancelled",
    };
    return statusMap[taskStatus] || "pending";
};

const getStatusTagType = (taskStatus) => {
    const typeMap = {
        待开始: "info",
        进行中: "warning",
        暂停: "info",
        已完成: "success",
        已取消: "danger",
    };
    return typeMap[taskStatus] || "info";
};

// 根据进展类型筛选时间线数据
const filteredTimelineData = computed(() => {
    if (activeProgressTab.value === "全部进展") {
        return timelineData.value;
    }
    return timelineData.value.filter((item) => item.progressType === activeProgressTab.value);
});

// 进展管理方法
const handleAddProgress = () => {
    progressDialogMode.value = "add";
    resetProgressForm();
    progressForm.progressType = activeProgressTab.value;
    // 设置eventId从当前查看的事件获取
    progressForm.eventId = eventForm.id;
    // 设置默认进展时间为当前时间
    progressForm.progressTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
    progressDialogVisible.value = true;
};

const handleEditProgress = (item) => {
    progressDialogMode.value = "edit";
    Object.assign(progressForm, { ...item });
    progressDialogVisible.value = true;
};

const handleDeleteProgress = async (item) => {
    try {
        // 调用删除接口
        await ProgressDelete({ ids: item.id });

        // 删除成功后更新本地数据
        const index = timelineData.value.findIndex((progress) => progress.id === item.id);
        if (index > -1) {
            timelineData.value.splice(index, 1);
            ElMessage.success("删除成功");
        }
    } catch (error) {
        ElMessage.error("删除失败：" + error.message);
    }
};

const handleSaveProgress = async () => {
    progressFormRef.value.validate(async (valid) => {
        if (valid) {
            try {
                // 调用保存接口
                await saveProgressData();

                if (progressDialogMode.value === "add") {
                    ElMessage.success("新增成功");
                } else {
                    ElMessage.success("保存成功");
                }
                progressDialogVisible.value = false;

                // 重新加载进展列表
                await loadProgressList();
            } catch (error) {
                ElMessage.error("保存失败：" + error.message);
            }
        }
    });
};

const resetProgressForm = () => {
    Object.assign(progressForm, {
        id: null,
        eventId: null,
        progressStage: "",
        taskContent: "",
        taskReceiver: "",
        contactPhone: "",
        progressTime: null,
        taskStatus: "待开始",
        completionRate: 0,
        priorityLevel: "一般",
        expectedCompletionTime: null,
        actualCompletionTime: null,
        remarks: "",
        attachmentUrl: "",
        progressType: "全部进展",
    });
};

// API调用函数
const saveProgressData = async () => {
    const params = { ...progressForm };
    // 如果是新增，不传id
    if (progressDialogMode.value === "add") {
        delete params.id;
    }
    // 删除前端用于分类的字段
    delete params.progressType;

    const res = await ProgressSave(params);
    return res;
};

const loadProgressList = async () => {
    try {
        const params = {
            eventId: eventForm.id,
        };
        const res = await ProgressList(params);
        if (res && res.data) {
            timelineData.value = res.data.map((item) => ({
                ...item,
                progressType: "全部进展", // 默认分类
            }));
        }
    } catch (error) {
        console.error("加载进展列表失败：", error);
        ElMessage.error("加载进展列表失败");
    }
};

const handleSelectionChange = (val) => {
    multipleSelection.value = val;
};

const onSubmit = () => {
    currentPage.value = 1;
    getalarmDesc(1, pageSize.value);
};

const onReset = () => {
    currentPage.value = 1;
    pageSize.value = 10;
    searchForm.ruleName = "";
    searchForm.systemCode = "";
    searchForm.deal = undefined;
    getalarmDesc(1, 10);
};

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const background = ref(false);
const disabled = ref(false);

const handleSizeChange = (val) => {
    pageSize.value = val;
    getalarmDesc(currentPage.value, val);
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
    getalarmDesc(val, pageSize.value);
};

onMounted(() => {
    // 初始化已经在 getalarmDesc() 中调用，这里不需要重复调用
});

const handleAdd = () => {
    dialogMode.value = "add";
    activeTab.value = "basic";
    resetForm();
    eventDialogVisible.value = true;
};

const handleView = async (row) => {
    dialogMode.value = "view";
    activeTab.value = "basic";
    loadEventData(row);
    eventDialogVisible.value = true;

    // 加载该事件的进展列表
    await loadProgressList();
};

const handleEdit = (row) => {
    dialogMode.value = "edit";
    activeTab.value = "basic";
    loadEventData(row);
    eventDialogVisible.value = true;
};

const handleDelete = (row) => {
    ElMessage.success("删除成功");
    getalarmDesc(currentPage.value, pageSize.value);
};

const resetForm = () => {
    Object.keys(eventForm).forEach((key) => {
        if (key === "level") {
            eventForm[key] = 1;
        } else if (key === "deal") {
            eventForm[key] = 0;
        } else if (key === "createDate" || key === "resetDate") {
            eventForm[key] = null;
        } else {
            eventForm[key] = "";
        }
    });
};

const loadEventData = (row) => {
    Object.keys(eventForm).forEach((key) => {
        if (row[key] !== undefined) {
            eventForm[key] = row[key];
        }
    });

    // 设置默认值用于演示
    if (!eventForm.alarmAttr) eventForm.alarmAttr = "温度传感器";
    if (!eventForm.reportedVal) eventForm.reportedVal = "45.2°C";
    if (!eventForm.threshold) eventForm.threshold = "设备温度超过正常范围，可能存在火灾风险";
    if (!eventForm.remark) eventForm.remark = "需要立即检查设备状态，确保安全";
    if (!eventForm.alarmTag) eventForm.alarmTag = "高温告警";
    if (!eventForm.productName) eventForm.productName = "智能烟感系统";
    if (!eventForm.productCode) eventForm.productCode = "PROD_001";
};

const handleSaveEvent = () => {
    if (
        !eventForm.ruleName ||
        !eventForm.deviceCode ||
        !eventForm.systemCode ||
        !eventForm.level ||
        !eventForm.createDate ||
        !eventForm.deviceAddress ||
        !eventForm.deviceName ||
        !eventForm.threshold
    ) {
        ElMessage({
            message: "请填写完整信息",
            type: "warning",
        });
        return;
    }

    ElMessage({
        message: dialogMode.value === "add" ? "新增成功" : "保存成功",
        type: "success",
    });
    eventDialogVisible.value = false;
    getalarmDesc(currentPage.value, pageSize.value);
};

const handleTabClick = (tab) => {
    activeTab.value = tab.name;
};

const changeProgressTab = (tab) => {
    activeProgressTab.value = tab;
};

const handleUpload = (file) => {
    ElMessage({
        message: "照片上传成功",
        type: "success",
    });
    return false;
};

// 进展文件上传
const uploadProgressFile = (file) => {
    const formData = new FormData();
    formData.append("file", file);

    bupload(formData)
        .then((res) => {
            ElMessage({
                message: "文件上传成功",
                type: "success",
            });
            progressForm.attachmentUrl = res.data;
        })
        .catch((error) => {
            console.error("文件上传出错", error);
            ElMessage({
                message: "文件上传出错",
                type: "error",
            });
        });
    return false;
};

// 进展文件下载
const downloadProgressFile = async (fileName) => {
    try {
        const response = await DrillDownload({ fileName });
        ElMessage.success("文档下载成功");
    } catch (error) {
        console.error("下载文件失败:", error);
        ElMessage.error("文档下载失败");
    }
};

// 移除进展文件
const removeProgressFile = () => {
    progressForm.attachmentUrl = "";
    ElMessage.success("文件删除成功");
};

// 获取文件名
const getFileName = (url) => {
    if (!url) return "";
    return url.split("/").pop() || url;
};
</script>

<style scoped lang="scss">
.right {
    width: calc(100% - 336px);
}
.body {
    padding: 50px 50px;
    height: calc(100% - 122px);
    position: relative;
    .title {
        margin-bottom: 50px;
        font-size: 20px;
        font-weight: bold;
    }
}

.search-box {
    height: 124px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 62px;
    background: #ffffff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;

    .add-btn {
        margin-left: auto;
    }
}

.table-box {
    margin-top: 40px;
    height: calc(100% - 214px);
    padding: 30px 62px;
    background: #fff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;
    .page-box {
        display: flex;
        justify-content: flex-end;
        margin-top: 30px;
    }
}

.el-form-item {
    margin-bottom: 0;
}

.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}

/* 事件弹窗样式优化 */
.event-dialog {
    .el-dialog {
        border-radius: 12px;
    }

    .el-dialog__header {
        padding: 24px 24px 16px;
        border-bottom: 1px solid #ebeef5;

        .el-dialog__title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-dialog__footer {
        padding: 16px 24px 24px;
        border-top: 1px solid #ebeef5;
    }
}

.dialog-content {
    padding: 24px;
}

.event-form {
    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 24px;

        .el-form-item {
            flex: 1;
            margin-bottom: 0;
        }
    }

    .el-form-item {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .el-form-item__label {
        font-weight: 500;
        color: #606266;
        line-height: 40px;

        &::before {
            color: #f56c6c;
            margin-right: 4px;
        }
    }

    .el-input,
    .el-select,
    .el-textarea {
        .el-input__wrapper {
            border-radius: 8px;
            transition: all 0.3s;

            &:hover {
                box-shadow: 0 0 0 1px #409eff inset;
            }
        }
    }

    .el-textarea .el-textarea__inner {
        border-radius: 8px;
        padding: 12px 16px;
        line-height: 1.5;
        font-size: 14px;
    }
}

.photos-section {
    .photos-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 12px;
        margin-bottom: 16px;

        .photo-item {
            width: 100%;
            height: 80px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}

.upload-demo {
    width: 100%;

    .el-upload {
        width: 100%;
    }

    .el-upload-dragger {
        width: 100%;
        height: 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        border: 2px dashed #d9d9d9;
        transition: all 0.3s;

        &:hover {
            border-color: #409eff;
            background-color: #f5f7fa;
        }
    }

    .el-icon--upload {
        font-size: 24px;
        color: #409eff;
        margin-bottom: 12px;
    }

    .el-upload__text {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
    }

    .attachment-container {
        .uploaded-file {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background-color: #f5f7fa;
            border: 1px solid #e4e7ed;
            border-radius: 8px;

            .file-info {
                display: flex;
                align-items: center;
                gap: 8px;

                .file-name {
                    color: #606266;
                    font-size: 14px;
                }
            }

            .file-actions {
                display: flex;
                gap: 8px;
            }
        }
    }
}

.progress-box {
    .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .progress-tabs {
        display: flex;
        gap: 20px;

        .progress-tab {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            color: #666;

            &.active {
                background: #409eff;
                color: white;
                border-color: #409eff;
            }

            &:hover {
                border-color: #409eff;
            }
        }
    }

    .timeline-container {
        position: relative;

        &::before {
            content: "";
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e4e7ed;
        }

        .timeline-item {
            position: relative;
            padding-left: 50px;
            margin-bottom: 30px;

            &:last-child {
                margin-bottom: 0;
            }

            .timeline-dot {
                position: absolute;
                left: 6px;
                top: 8px;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: #409eff;
                border: 3px solid white;
                box-shadow: 0 0 0 2px #409eff;

                &.emergency {
                    background: #f56c6c;
                    box-shadow: 0 0 0 2px #f56c6c;
                }

                &.alarm {
                    background: #e6a23c;
                    box-shadow: 0 0 0 2px #e6a23c;
                }

                &.incident {
                    background: #67c23a;
                    box-shadow: 0 0 0 2px #67c23a;
                }
            }

            .timeline-content {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #409eff;

                .timeline-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;

                    .timeline-time {
                        color: #909399;
                        font-size: 12px;
                    }

                    .timeline-actions {
                        display: flex;
                        gap: 8px;
                    }
                }

                .timeline-title {
                    font-weight: bold;
                    color: #303133;
                    margin-bottom: 8px;
                    display: flex;
                    align-items: center;
                }

                .timeline-desc {
                    color: #606266;
                    font-size: 14px;
                    line-height: 1.5;
                    margin-bottom: 8px;
                }

                .timeline-meta {
                    margin-top: 8px;
                    padding-top: 8px;
                    border-top: 1px solid #ebeef5;

                    .timeline-contact {
                        color: #909399;
                        font-size: 12px;
                        margin-bottom: 3px;
                    }
                }
            }

            // 不同状态的时间线点样式
            .timeline-dot {
                &.pending {
                    background: #909399;
                    box-shadow: 0 0 0 2px #909399;
                }

                &.processing {
                    background: #e6a23c;
                    box-shadow: 0 0 0 2px #e6a23c;
                }

                &.paused {
                    background: #c0c4cc;
                    box-shadow: 0 0 0 2px #c0c4cc;
                }

                &.completed {
                    background: #67c23a;
                    box-shadow: 0 0 0 2px #67c23a;
                }

                &.cancelled {
                    background: #f56c6c;
                    box-shadow: 0 0 0 2px #f56c6c;
                }
            }

            .timeline-progress {
                margin-top: 12px;
                margin-bottom: 8px;

                .progress-label {
                    font-size: 12px;
                    color: #606266;
                    margin-bottom: 6px;
                }
            }
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;

    .el-button {
        padding: 12px 32px;
        border-radius: 8px;
        font-weight: 500;
        min-width: 100px;

        &.el-button--primary {
            background: linear-gradient(135deg, #409eff 0%, #367cc7 100%);
            border-color: #409eff;

            &:hover {
                background: linear-gradient(135deg, #66b1ff 0%, #5a8dd1 100%);
            }
        }

        &:not(.el-button--primary) {
            border-color: #dcdfe6;
            color: #606266;

            &:hover {
                border-color: #409eff;
                color: #409eff;
            }
        }
    }
}

/* 进展弹窗样式 */
.progress-dialog {
    .el-dialog__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px 24px;
        border-radius: 12px 12px 0 0;

        .el-dialog__title {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
        }

        .el-dialog__headerbtn {
            .el-dialog__close {
                color: #ffffff;
                font-size: 18px;

                &:hover {
                    color: #f0f0f0;
                }
            }
        }
    }

    .el-dialog__body {
        padding: 0;
    }
}

.progress-dialog-content {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.progress-form {
    .form-section {
        margin-bottom: 32px;

        &:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e4e7ed;
            position: relative;

            &::before {
                content: "";
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 40px;
                height: 2px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }
        }
    }

    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .form-item-half {
            flex: 1;
            margin-bottom: 0;
        }
    }

    .el-form-item {
        margin-bottom: 20px;

        .el-form-item__label {
            font-weight: 500;
            color: #606266;
            line-height: 40px;

            &::before {
                color: #f56c6c;
                margin-right: 4px;
            }
        }

        .el-input,
        .el-select,
        .el-textarea {
            .el-input__wrapper {
                border-radius: 8px;
                transition: all 0.3s;

                &:hover {
                    box-shadow: 0 0 0 1px #667eea inset;
                }
            }
        }

        .el-textarea .el-textarea__inner {
            border-radius: 8px;
            padding: 12px 16px;
            line-height: 1.6;
            font-size: 14px;
        }
    }
}

.handler-list {
    .handler-empty {
        text-align: center;
        padding: 40px 20px;
        background: #f8f9fa;
        border: 2px dashed #ddd;
        border-radius: 12px;
        margin-bottom: 16px;

        .empty-text {
            color: #909399;
            font-size: 14px;
            margin-bottom: 16px;
        }
    }

    .handler-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 16px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        border: 1px solid #e4e7ed;
        transition: all 0.3s;

        &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
        }

        .handler-inputs {
            display: flex;
            gap: 12px;
            flex: 1;

            .el-input {
                flex: 1;
            }
        }
    }

    .add-handler-btn {
        width: 100%;
        margin-top: 8px;
        padding: 12px;
        border: 2px dashed #667eea;
        border-radius: 8px;
        background: transparent;
        transition: all 0.3s;

        &:hover {
            background: #f0f4ff;
            border-color: #4c63d2;
        }
    }
}

.status-option {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;

        &.pending {
            background: #909399;
        }

        &.processing {
            background: #e6a23c;
        }

        &.paused {
            background: #c0c4cc;
        }

        &.completed {
            background: #67c23a;
        }

        &.cancelled {
            background: #f56c6c;
        }
    }
}

.progress-dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 20px 24px;
    border-top: 1px solid #ebeef5;
    background: #fafafa;

    .el-button {
        padding: 12px 32px;
        border-radius: 8px;
        font-weight: 500;
        min-width: 120px;

        &.el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;

            &:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            }
        }

        &:not(.el-button--primary) {
            border-color: #dcdfe6;
            color: #606266;

            &:hover {
                border-color: #667eea;
                color: #667eea;
            }
        }
    }
}
</style>
