/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-02-25 11:35:29
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-05-06 14:49:20
 * @FilePath: \evacuation-simulation-backend\src\apis\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { instanceClient, fileClient, instanceXc } from "@/public/client";
// 获取token
export const getToken = (params) =>
    instanceClient.get("/token/generate?appid=client-app&secret=123456", { params });

export const YjylList = (params) => instanceClient.get("/api/yjyl/list", { params });
export const YjylDel = (params) => instanceClient.get("/api/yjyl/delete", { params });
export const YjylDetail = (params) => instanceClient.get("/api/yjyl/queryById", { params });

export const YjyaList = (params) => instanceClient.get("/api/yjya/list", { params });
export const YjyaDel = (params) => instanceClient.get("/api/yjya/delete", { params });
export const YjyaDetail = (params) => instanceClient.get("/api/yjya/queryById", { params });
export const YjyaExport = (params) => fileClient.get("/api/yjya/exportById", { params });
export const YjyaUpload = (params) => instanceClient.post("/api/uploadFileAndCreateYa", params);
export const YjyaDownload = (params) => fileClient.get("/api/download", { params });

export const DeviceList = (params) => instanceClient.get("/api/device/list", { params });
export const AqckList = (params) => instanceClient.get("/api/aqck/list", { params });

// 应急事件进展相关接口
export const ProgressList = (params) =>
    instanceClient.get("/api/yjsj/progress/getByEventId", { params });
export const ProgressSave = (params) => instanceClient.post("/api/yjsj/progress/save", params);
export const ProgressDelete = (params) =>
    instanceClient.get("/api/yjsj/progress/delete", { params });

export const alarmDesc = (params) => instanceXc.post("/alarmDesc/data-page", params);

// 应急演练相关接口
export const DrillList = (params) => instanceClient.get("/api/ioc_yjyl/list", { params });
export const DrillAdd = (params) => instanceClient.post("/api/ioc_yjyl/add", params);
export const DrillComplete = (params) => instanceClient.post("/api/ioc_yjyl/complete", params);
export const Drilldelete = (params) => instanceClient.get("/api/ioc_yjyl/delete", { params });
export const DrillDownload = (params) => fileClient.get("/api/download", { params });
export const bupload = (params) => instanceClient.post("/api/upload", params);

export const listKey = (params) =>
    instanceClient.get("/api/dict/listKey?dictKey=event_type", { params });

// IOC结构相关接口
export const IocStructSave = (params) => instanceClient.post("/api/ioc_struct/save", params);
export const IocStructGetTree = (params) => instanceClient.get("/api/ioc_struct/get_tree_by_eventcode", { params });
export const IocStructDelete = (params) => instanceClient.get("/api/ioc_struct/delete", { params });



