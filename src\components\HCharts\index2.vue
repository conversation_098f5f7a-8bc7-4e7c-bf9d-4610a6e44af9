<!--
 * @Author: your name
 * @Date: 2021-09-16 16:28:53
 * @LastEditTime: 2021-10-08 14:23:10
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \ecological-cultural-tourism\src\components\ECharts\index.vue
-->
<template>
    <div ref="chart"></div>
</template>

<script>
import Highcharts from "highcharts/highstock";
import HighchartsMore from "highcharts/highcharts-more";
import HighchartsDrilldown from "highcharts/modules/drilldown";
import Highcharts3D from "highcharts/highcharts-3d";

HighchartsMore(Highcharts);
HighchartsDrilldown(Highcharts);
Highcharts3D(Highcharts);
// 全局配置，针对页面上所有图表有效
Highcharts.setOptions({
    chart: {
        backgroundColor: "rgba(255, 255, 255, 0)",
        borderWidth: 0,
        plotBackgroundColor: "rgba(255, 255, 255, 0)",
        plotShadow: false,
        plotBorderWidth: 0,
    },
    credits: {
        enabled: false,
    },
});

export default {
    name: "ECharts",
    props: {
        options: Object,
        theme: String,
    },
    data() {
        return {
            chart: null,
        };
    },
    watch: {
        options: {
            immediate: true,
            handler: "$_setOption",
        },
        chart: "$_setOption",
    },
    beforeUnmount() {
        this.removeResize();
        // this.chart?.dispose();
        this.chart = null;
    },
    // beforeDestroy() {
    //     this.removeResize();
    //     this.chart?.dispose();
    //     this.chart = null;
    // },
    mounted() {
        this.$_init();
    },
    methods: {
        resize() {
            this.chart.resize();
        },
        removeResize() {
            // this.$adaption.delete(this.resize);
        },
        $_init() {
            window.chart = Highcharts.chart(this.$refs.chart, this.options);
            // this.$adaption.add(this.resize);
        },
        $_setOption() {
            if (this.options && this.chart) {
                this.chart.setSeriesData(this.options.series);
                // this.chart.setOptions(this.options);
            }
        },
    },
};
</script>

<style scoped></style>
