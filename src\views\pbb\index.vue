<template>
    <div class="right">
        <BaseTitle />
        <div class="body">
            <div class="title">排班表</div>
            <div class="search-box">
                <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                    <el-form-item label="排班表名称">
                        <el-input v-model="searchForm.name" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="onReset">重置</el-button>
                        <el-button type="primary" @click="onSubmit">查询</el-button>
                    </el-form-item>
                </el-form>
                <div class="action-buttons">
                    <el-button type="primary" @click="handleImport">导入排班表</el-button>
                </div>
            </div>
            <div class="table-box">
                <el-table
                    ref="tableRef"
                    :data="tableData"
                    style="width: 100%"
                    :height="tableHeight"
                    :loading="tableLoading"
                    :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: '600',
                        fontSize: '14px',
                        height: '50px',
                    }"
                    :row-style="{ height: '48px' }"
                    :cell-style="{ fontSize: '13px', color: '#606266' }"
                >
                    <el-table-column type="index" label="序号" width="80" />
                    <el-table-column prop="name" label="排班表名称" width="300" />
                    <el-table-column prop="schedule" label="排班时间" width="400" />
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button type="text" @click="handleView(scope.row)">详情</el-button>
                            <el-popconfirm title="确认删除?" @confirm="handleDelete(scope.row)">
                                <template #reference>
                                    <el-button type="text" style="color: #f56c6c">删除</el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50]"
                    :disabled="disabled"
                    :background="background"
                    layout=" prev, pager, next, total, sizes, jumper"
                    :total="total"
                    class="page-box"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>

        <!-- 详情弹框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="排班表详情"
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form :model="detailData" label-width="120px">
                <el-form-item label="排班表名称">
                    <span class="form-value">{{ detailData.name || "-" }}</span>
                </el-form-item>
                <el-form-item label="排班时间">
                    <span class="form-value">{{ detailData.schedule || "-" }}</span>
                </el-form-item>
                <el-form-item label="创建时间">
                    <span class="form-value">{{ detailData.createTime || "-" }}</span>
                </el-form-item>
                <el-form-item label="更新时间">
                    <span class="form-value">{{ detailData.updateTime || "-" }}</span>
                </el-form-item>
                <el-form-item label="备注">
                    <span class="form-value">{{ detailData.remark || "-" }}</span>
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 导入弹框 -->
        <el-dialog
            v-model="uploadDialogVisible"
            title="导入排班表"
            width="500px"
            :close-on-click-modal="false"
        >
            <el-upload
                class="upload-demo"
                action=""
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :file-list="uploadFileList"
                multiple
                drag
            >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">将文件拖拽到此处，或<em>点击上传</em></div>
                <template #tip>
                    <div class="el-upload__tip">支持 Excel 格式文件，单个文件不超过 10MB</div>
                </template>
            </el-upload>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="uploadDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmUpload">确认导入</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import BaseTitle from "@/components/BaseTitle/index.vue";
import { ElMessage } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";

const router = useRouter();

// 表格数据
const tableRef = ref();
const tableData = ref([]);
const tableLoading = ref(false);

// 计算表格高度
const tableHeight = computed(() => {
    return "calc(100vh - 320px)";
});

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const background = ref(false);
const disabled = ref(false);

// 搜索表单
const searchForm = reactive({
    name: "",
});

// 弹框相关
const detailDialogVisible = ref(false);
const uploadDialogVisible = ref(false);
const uploadFileList = ref([]);
const detailData = ref({});

// 初始化数据
const initData = () => {
    tableData.value = [
        {
            id: 1,
            name: "10月份第一周排班",
            schedule: "2024.10.01-2024.10.07",
            createTime: "2024.10.01 10:00:00",
            updateTime: "2024.10.01 10:00:00",
            remark: "第一周值班安排",
        },
        {
            id: 2,
            name: "10月份第一周排班",
            schedule: "2024.10.01-2024.10.07",
            createTime: "2024.10.01 10:00:00",
            updateTime: "2024.10.01 10:00:00",
            remark: "第一周值班安排",
        },
        {
            id: 3,
            name: "10月份第一周排班",
            schedule: "2024.10.01-2024.10.07",
            createTime: "2024.10.01 10:00:00",
            updateTime: "2024.10.01 10:00:00",
            remark: "第一周值班安排",
        },
        {
            id: 4,
            name: "10月份第一周排班",
            schedule: "2024.10.01-2024.10.07",
            createTime: "2024.10.01 10:00:00",
            updateTime: "2024.10.01 10:00:00",
            remark: "第一周值班安排",
        },
        {
            id: 5,
            name: "10月份第一周排班",
            schedule: "2024.10.01-2024.10.07",
            createTime: "2024.10.01 10:00:00",
            updateTime: "2024.10.01 10:00:00",
            remark: "第一周值班安排",
        },
    ];
    total.value = tableData.value.length;
};

// 查询
const onSubmit = () => {
    currentPage.value = 1;
    handleSearch();
};

// 重置
const onReset = () => {
    Object.assign(searchForm, {
        name: "",
    });
    onSubmit();
};

// 搜索
const handleSearch = () => {
    tableLoading.value = true;
    setTimeout(() => {
        initData();
        tableLoading.value = false;
    }, 500);
};

// 分页处理
const handleSizeChange = (val) => {
    pageSize.value = val;
    handleSearch();
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
    handleSearch();
};

// 查看详情
const handleView = (row) => {
    detailData.value = { ...row };
    detailDialogVisible.value = true;
};

// 删除
const handleDelete = (row) => {
    ElMessage.success("删除成功");
    handleSearch();
};

// 导入相关
const handleImport = () => {
    uploadDialogVisible.value = true;
};

const beforeUpload = (file) => {
    const isExcel =
        file.type === "application/vnd.ms-excel" ||
        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    const isLt10M = file.size / 1024 / 1024 < 10;

    if (!isExcel) {
        ElMessage.error("只能上传 Excel 格式的文件!");
        return false;
    }
    if (!isLt10M) {
        ElMessage.error("上传文件大小不能超过 10MB!");
        return false;
    }
    return true;
};

const handleUploadSuccess = (response, file, fileList) => {
    ElMessage.success("文件上传成功");
    uploadFileList.value = fileList;
};

const handleUploadError = (error, file, fileList) => {
    ElMessage.error("文件上传失败");
};

const confirmUpload = () => {
    if (uploadFileList.value.length === 0) {
        ElMessage.warning("请选择要上传的文件");
        return;
    }

    ElMessage.success("导入成功");
    uploadDialogVisible.value = false;
    uploadFileList.value = [];
    handleSearch();
};

onMounted(() => {
    handleSearch();
});
</script>

<style scoped lang="scss">
.right {
    width: calc(100% - 336px);
}

.body {
    padding: 50px 50px;
    height: calc(100% - 122px);
    display: flex;
    flex-direction: column;

    .title {
        margin-bottom: 50px;
        font-size: 20px;
        font-weight: bold;
    }
}

.search-box {
    height: 84px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 62px;
    background: #ffffff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;

    .demo-form-inline {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-left: 20px;
    }
}

.table-box {
    margin-top: 40px;
    flex: 1;
    padding: 30px 62px;
    background: #fff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;
    display: flex;
    flex-direction: column;

    .page-box {
        display: flex;
        justify-content: flex-end;
        margin-top: 30px;
    }
}

.form-value {
    color: #303133;
    font-size: 14px;
    line-height: 32px;
}

.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    border-top: 1px solid #ebeef5;
    padding-top: 15px;

    .el-button {
        padding: 12px 32px;
        border-radius: 8px;
        font-weight: 500;
        min-width: 100px;

        &.el-button--primary {
            background: linear-gradient(135deg, #409eff 0%, #367cc7 100%);
            border-color: #409eff;

            &:hover {
                background: linear-gradient(135deg, #66b1ff 0%, #5a8dd1 100%);
            }
        }

        &:not(.el-button--primary) {
            border-color: #dcdfe6;
            color: #606266;

            &:hover {
                border-color: #409eff;
                color: #409eff;
            }
        }
    }
}

.upload-demo {
    width: 100%;

    .el-upload {
        width: 100%;
    }

    .el-upload-dragger {
        width: 100%;
        height: 120px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s;

        &:hover {
            border-color: #2993fd;
            background: #f5f9ff;
        }
    }
}

:deep(.el-table) {
    border: 1px solid #ebeef5;
    border-radius: 4px;
}

:deep(.el-table .el-table__header-wrapper) {
    border-radius: 4px 4px 0 0;
}

:deep(.el-table td, .el-table th) {
    border-bottom: 1px solid #ebeef5;
}

:deep(.el-table th) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table td) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table th:last-child) {
    border-right: none;
}

:deep(.el-table td:last-child) {
    border-right: none;
}

:deep(.el-dialog) {
    border-radius: 8px;
}

:deep(.el-dialog__header) {
    background: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    padding: 20px 24px;
}

:deep(.el-dialog__title) {
    color: #303133;
    font-weight: 600;
    font-size: 16px;
}

:deep(.el-dialog__body) {
    padding: 24px;
}

// 表单样式优化
.el-form {
    .el-form-item {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .el-form-item__label {
        font-weight: 500;
        color: #606266;
        line-height: 40px;

        &::before {
            color: #f56c6c;
            margin-right: 4px;
        }
    }

    .el-input,
    .el-select,
    .el-textarea {
        .el-input__wrapper {
            border-radius: 8px;
            transition: all 0.3s;

            &:hover {
                box-shadow: 0 0 0 1px #409eff inset;
            }
        }
    }

    .el-textarea .el-textarea__inner {
        border-radius: 8px;
        padding: 12px 16px;
        line-height: 1.5;
        font-size: 14px;
    }
}

:deep(.el-button) {
    border-radius: 8px;
    padding: 12px 16px;
    font-weight: 500;
}

:deep(.el-button--primary) {
    background: linear-gradient(135deg, #409eff 0%, #367cc7 100%);
    border-color: #409eff;

    &:hover {
        background: linear-gradient(135deg, #66b1ff 0%, #5a8dd1 100%);
    }

}

.el-form-item {
    margin-bottom: 0;
}

.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}
</style>
