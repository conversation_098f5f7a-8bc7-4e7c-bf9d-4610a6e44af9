{"name": "火灾疏散演练", "level": "高", "type": "火灾", "addr": "体育馆", "rypz": {"personTotal": 3500, "limitNum": 15016, "datas": [{"areaName": "201", "manNum": 7, "womanNum": 29, "maxNum": 600, "manSeatName": ["tyg_201区_17排09座", "tyg_201区_16排21座", "tyg_201区_1排02座", "tyg_201区_26排02座", "tyg_201区_9排21座", "tyg_201区_15排22座", "tyg_201区_27排08座"], "womanSeatName": ["tyg_201区_27排14座", "tyg_201区_3排23座", "tyg_201区_15排16座", "tyg_201区_27排20座", "tyg_201区_2排19座", "tyg_201区_13排20座", "tyg_201区_14排10座", "tyg_201区_6排26座", "tyg_201区_17排15座", "tyg_201区_1排23座", "tyg_201区_25排09座", "tyg_201区_6排15座", "tyg_201区_3排22座", "tyg_201区_26排16座", "tyg_201区_18排05座", "tyg_201区_25排20座", "tyg_201区_3排24座", "tyg_201区_6排07座", "tyg_201区_27排12座", "tyg_201区_6排01座", "tyg_201区_7排02座", "tyg_201区_8排02座", "tyg_201区_7排25座", "tyg_201区_12排11座", "tyg_201区_23排15座", "tyg_201区_8排11座", "tyg_201区_23排20座", "tyg_201区_17排17座", "tyg_201区_4排12座"]}, {"areaName": "202", "manNum": 7, "womanNum": 43, "maxNum": 486, "manSeatName": ["tyg_202区_9排12座", "tyg_202区_18排17座", "tyg_202区_3排03座", "tyg_202区_11排19座", "tyg_202区_2排06座", "tyg_202区_26排03座", "tyg_202区_18排16座"], "womanSeatName": ["tyg_202区_7排15座", "tyg_202区_12排16座", "tyg_202区_25排05座", "tyg_202区_14排18座", "tyg_202区_13排07座", "tyg_202区_26排13座", "tyg_202区_11排08座", "tyg_202区_6排06座", "tyg_202区_5排07座", "tyg_202区_18排03座", "tyg_202区_7排17座", "tyg_202区_2排18座", "tyg_202区_9排04座", "tyg_202区_15排08座", "tyg_202区_23排10座", "tyg_202区_12排15座", "tyg_202区_27排12座", "tyg_202区_18排21座", "tyg_202区_28排01座", "tyg_202区_5排18座", "tyg_202区_7排02座", "tyg_202区_10排19座", "tyg_202区_6排04座", "tyg_202区_3排10座", "tyg_202区_26排11座", "tyg_202区_15排02座", "tyg_202区_25排13座", "tyg_202区_23排12座", "tyg_202区_16排19座", "tyg_202区_26排17座", "tyg_202区_27排11座", "tyg_202区_15排10座", "tyg_202区_23排04座", "tyg_202区_9排02座", "tyg_202区_7排07座", "tyg_202区_12排05座", "tyg_202区_25排12座", "tyg_202区_4排14座", "tyg_202区_14排09座", "tyg_202区_3排06座", "tyg_202区_13排01座", "tyg_202区_14排15座", "tyg_202区_25排10座"]}, {"areaName": "203", "manNum": 24, "womanNum": 24, "maxNum": 389, "manSeatName": ["tyg_203区_16排14座", "tyg_203区_24排03座", "tyg_203区_9排06座", "tyg_203区_21排07座", "tyg_203区_6排05座", "tyg_203区_17排06座", "tyg_203区_25排09座", "tyg_203区_26排16座", "tyg_203区_7排05座", "tyg_203区_23排08座", "tyg_203区_23排14座", "tyg_203区_18排02座", "tyg_203区_2排04座", "tyg_203区_27排05座", "tyg_203区_18排16座", "tyg_203区_7排11座", "tyg_203区_8排10座", "tyg_203区_26排13座", "tyg_203区_20排03座", "tyg_203区_23排15座", "tyg_203区_8排11座", "tyg_203区_13排07座", "tyg_203区_19排13座", "tyg_203区_28排02座"], "womanSeatName": ["tyg_203区_16排01座", "tyg_203区_8排03座", "tyg_203区_22排13座", "tyg_203区_13排01座", "tyg_203区_28排11座", "tyg_203区_28排01座", "tyg_203区_12排10座", "tyg_203区_24排04座", "tyg_203区_23排09座", "tyg_203区_26排20座", "tyg_203区_24排10座", "tyg_203区_26排07座", "tyg_203区_23排03座", "tyg_203区_16排02座", "tyg_203区_14排12座", "tyg_203区_17排03座", "tyg_203区_23排01座", "tyg_203区_25排16座", "tyg_203区_6排03座", "tyg_203区_13排02座", "tyg_203区_4排02座", "tyg_203区_21排12座", "tyg_203区_17排04座", "tyg_203区_27排17座"]}, {"areaName": "204", "manNum": 40, "womanNum": 13, "maxNum": 216, "manSeatName": ["tyg_204区_28排10座", "tyg_204区_24排04座", "tyg_204区_25排13座", "tyg_204区_20排05座", "tyg_204区_16排07座", "tyg_204区_27排13座", "tyg_204区_28排06座", "tyg_204区_18排06座", "tyg_204区_16排12座", "tyg_204区_27排05座", "tyg_204区_19排02座", "tyg_204区_20排11座", "tyg_204区_17排09座", "tyg_204区_25排02座", "tyg_204区_16排09座", "tyg_204区_21排13座", "tyg_204区_18排05座", "tyg_204区_17排15座", "tyg_204区_28排03座", "tyg_204区_16排08座", "tyg_204区_10排01座", "tyg_204区_22排07座", "tyg_204区_26排07座", "tyg_204区_23排12座", "tyg_204区_27排07座", "tyg_204区_16排04座", "tyg_204区_23排01座", "tyg_204区_19排04座", "tyg_204区_18排17座", "tyg_204区_24排06座", "tyg_204区_24排10座", "tyg_204区_27排01座", "tyg_204区_27排22座", "tyg_204区_22排01座", "tyg_204区_20排12座", "tyg_204区_17排01座", "tyg_204区_20排07座", "tyg_204区_26排15座", "tyg_204区_20排01座", "tyg_204区_23排10座"], "womanSeatName": ["tyg_204区_28排04座", "tyg_204区_22排06座", "tyg_204区_27排18座", "tyg_204区_20排04座", "tyg_204区_22排03座", "tyg_204区_23排14座", "tyg_204区_18排03座", "tyg_204区_27排20座", "tyg_204区_27排04座", "tyg_204区_16排16座", "tyg_204区_19排10座", "tyg_204区_18排01座", "tyg_204区_23排08座"]}, {"areaName": "205", "manNum": 14, "womanNum": 29, "maxNum": 372, "manSeatName": ["tyg_205区_25排05座", "tyg_205区_19排05座", "tyg_205区_19排10座", "tyg_205区_5排03座", "tyg_205区_6排01座", "tyg_205区_28排04座", "tyg_205区_22排14座", "tyg_205区_13排13座", "tyg_205区_21排10座", "tyg_205区_15排03座", "tyg_205区_28排09座", "tyg_205区_18排09座", "tyg_205区_20排08座", "tyg_205区_17排11座"], "womanSeatName": ["tyg_205区_24排12座", "tyg_205区_17排09座", "tyg_205区_23排10座", "tyg_205区_23排12座", "tyg_205区_4排05座", "tyg_205区_15排10座", "tyg_205区_15排14座", "tyg_205区_28排19座", "tyg_205区_9排04座", "tyg_205区_9排06座", "tyg_205区_17排05座", "tyg_205区_9排12座", "tyg_205区_27排13座", "tyg_205区_23排08座", "tyg_205区_26排10座", "tyg_205区_26排15座", "tyg_205区_21排12座", "tyg_205区_24排01座", "tyg_205区_4排01座", "tyg_205区_21排08座", "tyg_205区_28排20座", "tyg_205区_16排15座", "tyg_205区_4排04座", "tyg_205区_26排12座", "tyg_205区_17排06座", "tyg_205区_11排07座", "tyg_205区_18排05座", "tyg_205区_15排12座", "tyg_205区_3排04座"]}, {"areaName": "206", "manNum": 18, "womanNum": 45, "maxNum": 686, "manSeatName": ["tyg_206区_23排20座", "tyg_206区_5排04座", "tyg_206区_22排12座", "tyg_206区_8排06座", "tyg_206区_20排04座", "tyg_206区_11排05座", "tyg_206区_8排22座", "tyg_206区_24排11座", "tyg_206区_21排14座", "tyg_206区_16排01座", "tyg_206区_17排18座", "tyg_206区_27排17座", "tyg_206区_7排24座", "tyg_206区_7排11座", "tyg_206区_28排08座", "tyg_206区_14排17座", "tyg_206区_7排06座", "tyg_206区_6排16座"], "womanSeatName": ["tyg_206区_2排16座", "tyg_206区_15排08座", "tyg_206区_2排01座", "tyg_206区_14排21座", "tyg_206区_15排06座", "tyg_206区_12排02座", "tyg_206区_1排04座", "tyg_206区_10排19座", "tyg_206区_11排13座", "tyg_206区_11排19座", "tyg_206区_18排09座", "tyg_206区_9排15座", "tyg_206区_14排11座", "tyg_206区_15排05座", "tyg_206区_1排03座", "tyg_206区_19排05座", "tyg_206区_17排14座", "tyg_206区_21排16座", "tyg_206区_18排21座", "tyg_206区_19排18座", "tyg_206区_8排03座", "tyg_206区_28排05座", "tyg_206区_14排19座", "tyg_206区_14排22座", "tyg_206区_8排10座", "tyg_206区_21排07座", "tyg_206区_14排26座", "tyg_206区_9排03座", "tyg_206区_5排23座", "tyg_206区_28排11座", "tyg_206区_17排06座", "tyg_206区_6排04座", "tyg_206区_24排02座", "tyg_206区_22排17座", "tyg_206区_19排02座", "tyg_206区_27排07座", "tyg_206区_3排16座", "tyg_206区_16排07座", "tyg_206区_26排14座", "tyg_206区_8排13座", "tyg_206区_7排19座", "tyg_206区_11排24座", "tyg_206区_9排26座", "tyg_206区_1排24座", "tyg_206区_4排16座"]}, {"areaName": "207", "manNum": 40, "womanNum": 21, "maxNum": 375, "manSeatName": ["tyg_207区_5排02座", "tyg_207区_9排09座", "tyg_207区_10排05座", "tyg_207区_6排07座", "tyg_207区_26排17座", "tyg_207区_20排07座", "tyg_207区_17排13座", "tyg_207区_11排01座", "tyg_207区_1排03座", "tyg_207区_17排01座", "tyg_207区_7排02座", "tyg_207区_23排12座", "tyg_207区_24排12座", "tyg_207区_1排04座", "tyg_207区_11排12座", "tyg_207区_27排08座", "tyg_207区_6排05座", "tyg_207区_25排11座", "tyg_207区_15排03座", "tyg_207区_24排11座", "tyg_207区_9排04座", "tyg_207区_26排06座", "tyg_207区_25排08座", "tyg_207区_23排09座", "tyg_207区_19排12座", "tyg_207区_28排08座", "tyg_207区_8排08座", "tyg_207区_22排04座", "tyg_207区_3排04座", "tyg_207区_23排03座", "tyg_207区_25排01座", "tyg_207区_28排06座", "tyg_207区_15排02座", "tyg_207区_20排01座", "tyg_207区_7排07座", "tyg_207区_18排14座", "tyg_207区_26排04座", "tyg_207区_12排05座", "tyg_207区_25排02座", "tyg_207区_22排07座"], "womanSeatName": ["tyg_207区_7排10座", "tyg_207区_18排09座", "tyg_207区_14排09座", "tyg_207区_11排09座", "tyg_207区_25排05座", "tyg_207区_18排05座", "tyg_207区_15排10座", "tyg_207区_28排07座", "tyg_207区_12排02座", "tyg_207区_6排01座", "tyg_207区_28排04座", "tyg_207区_27排07座", "tyg_207区_23排06座", "tyg_207区_4排06座", "tyg_207区_27排14座", "tyg_207区_28排26座", "tyg_207区_26排10座", "tyg_207区_18排11座", "tyg_207区_16排05座", "tyg_207区_13排07座", "tyg_207区_7排05座"]}, {"areaName": "208", "manNum": 37, "womanNum": 25, "maxNum": 216, "manSeatName": ["tyg_208区_16排10座", "tyg_208区_25排03座", "tyg_208区_19排06座", "tyg_208区_28排09座", "tyg_208区_26排07座", "tyg_208区_18排05座", "tyg_208区_25排13座", "tyg_208区_27排08座", "tyg_208区_18排12座", "tyg_208区_13排02座", "tyg_208区_11排02座", "tyg_208区_28排04座", "tyg_208区_27排22座", "tyg_208区_21排04座", "tyg_208区_25排10座", "tyg_208区_16排12座", "tyg_208区_19排07座", "tyg_208区_24排10座", "tyg_208区_20排04座", "tyg_208区_21排09座", "tyg_208区_26排01座", "tyg_208区_26排09座", "tyg_208区_25排12座", "tyg_208区_26排11座", "tyg_208区_23排13座", "tyg_208区_19排04座", "tyg_208区_28排22座", "tyg_208区_19排03座", "tyg_208区_25排04座", "tyg_208区_25排15座", "tyg_208区_12排01座", "tyg_208区_19排01座", "tyg_208区_18排02座", "tyg_208区_24排04座", "tyg_208区_17排01座", "tyg_208区_17排15座", "tyg_208区_16排16座"], "womanSeatName": ["tyg_208区_28排19座", "tyg_208区_26排08座", "tyg_208区_16排05座", "tyg_208区_21排05座", "tyg_208区_12排02座", "tyg_208区_17排09座", "tyg_208区_20排07座", "tyg_208区_18排14座", "tyg_208区_20排03座", "tyg_208区_24排02座", "tyg_208区_26排10座", "tyg_208区_16排11座", "tyg_208区_28排06座", "tyg_208区_19排09座", "tyg_208区_24排09座", "tyg_208区_25排08座", "tyg_208区_28排15座", "tyg_208区_28排16座", "tyg_208区_22排09座", "tyg_208区_16排02座", "tyg_208区_23排14座", "tyg_208区_22排03座", "tyg_208区_21排08座", "tyg_208区_27排10座", "tyg_208区_28排10座"]}, {"areaName": "209", "manNum": 47, "womanNum": 16, "maxNum": 399, "manSeatName": ["tyg_209区_13排14座", "tyg_209区_18排02座", "tyg_209区_20排12座", "tyg_209区_25排03座", "tyg_209区_16排14座", "tyg_209区_16排12座", "tyg_209区_18排15座", "tyg_209区_14排08座", "tyg_209区_24排17座", "tyg_209区_11排07座", "tyg_209区_11排03座", "tyg_209区_28排03座", "tyg_209区_24排15座", "tyg_209区_22排07座", "tyg_209区_9排03座", "tyg_209区_12排08座", "tyg_209区_21排02座", "tyg_209区_5排03座", "tyg_209区_20排13座", "tyg_209区_11排01座", "tyg_209区_12排09座", "tyg_209区_25排16座", "tyg_209区_15排07座", "tyg_209区_24排14座", "tyg_209区_19排04座", "tyg_209区_28排26座", "tyg_209区_23排04座", "tyg_209区_24排01座", "tyg_209区_28排22座", "tyg_209区_6排03座", "tyg_209区_23排14座", "tyg_209区_21排03座", "tyg_209区_9排05座", "tyg_209区_17排03座", "tyg_209区_3排04座", "tyg_209区_23排05座", "tyg_209区_7排03座", "tyg_209区_14排15座", "tyg_209区_14排02座", "tyg_209区_22排02座", "tyg_209区_26排01座", "tyg_209区_28排14座", "tyg_209区_13排10座", "tyg_209区_5排07座", "tyg_209区_28排02座", "tyg_209区_28排23座", "tyg_209区_22排12座"], "womanSeatName": ["tyg_209区_27排12座", "tyg_209区_18排12座", "tyg_209区_24排06座", "tyg_209区_19排12座", "tyg_209区_13排07座", "tyg_209区_28排17座", "tyg_209区_16排11座", "tyg_209区_27排06座", "tyg_209区_20排11座", "tyg_209区_26排23座", "tyg_209区_27排23座", "tyg_209区_18排17座", "tyg_209区_8排08座", "tyg_209区_5排01座", "tyg_209区_16排03座", "tyg_209区_27排08座"]}, {"areaName": "210", "manNum": 8, "womanNum": 51, "maxNum": 357, "manSeatName": ["tyg_210区_15排09座", "tyg_210区_6排01座", "tyg_210区_2排04座", "tyg_210区_6排14座", "tyg_210区_13排11座", "tyg_210区_8排07座", "tyg_210区_5排14座", "tyg_210区_3排01座"], "womanSeatName": ["tyg_210区_13排07座", "tyg_210区_7排18座", "tyg_210区_1排13座", "tyg_210区_5排05座", "tyg_210区_10排20座", "tyg_210区_1排10座", "tyg_210区_3排05座", "tyg_210区_14排11座", "tyg_210区_10排04座", "tyg_210区_16排06座", "tyg_210区_14排06座", "tyg_210区_1排14座", "tyg_210区_4排10座", "tyg_210区_1排01座", "tyg_210区_12排17座", "tyg_210区_16排14座", "tyg_210区_9排20座", "tyg_210区_12排12座", "tyg_210区_1排02座", "tyg_210区_5排04座", "tyg_210区_5排01座", "tyg_210区_5排20座", "tyg_210区_15排16座", "tyg_210区_2排17座", "tyg_210区_16排10座", "tyg_210区_15排19座", "tyg_210区_5排11座", "tyg_210区_12排05座", "tyg_210区_17排10座", "tyg_210区_17排07座", "tyg_210区_12排08座", "tyg_210区_3排13座", "tyg_210区_11排20座", "tyg_210区_14排12座", "tyg_210区_4排20座", "tyg_210区_13排08座", "tyg_210区_12排10座", "tyg_210区_7排08座", "tyg_210区_12排01座", "tyg_210区_16排13座", "tyg_210区_7排09座", "tyg_210区_17排21座", "tyg_210区_14排02座", "tyg_210区_9排19座", "tyg_210区_8排14座", "tyg_210区_11排18座", "tyg_210区_17排20座", "tyg_210区_11排15座", "tyg_210区_2排08座", "tyg_210区_14排04座", "tyg_210区_9排06座"]}, {"areaName": "211", "manNum": 39, "womanNum": 16, "maxNum": 442, "manSeatName": ["tyg_211区_14排17座", "tyg_211区_13排24座", "tyg_211区_12排11座", "tyg_211区_2排04座", "tyg_211区_16排18座", "tyg_211区_15排14座", "tyg_211区_10排25座", "tyg_211区_15排25座", "tyg_211区_10排11座", "tyg_211区_7排17座", "tyg_211区_15排04座", "tyg_211区_11排05座", "tyg_211区_11排11座", "tyg_211区_5排20座", "tyg_211区_2排02座", "tyg_211区_15排08座", "tyg_211区_13排16座", "tyg_211区_5排16座", "tyg_211区_6排10座", "tyg_211区_16排04座", "tyg_211区_6排06座", "tyg_211区_3排26座", "tyg_211区_14排02座", "tyg_211区_13排07座", "tyg_211区_5排03座", "tyg_211区_1排14座", "tyg_211区_6排05座", "tyg_211区_2排09座", "tyg_211区_14排13座", "tyg_211区_10排26座", "tyg_211区_7排02座", "tyg_211区_6排07座", "tyg_211区_10排02座", "tyg_211区_10排23座", "tyg_211区_8排23座", "tyg_211区_2排13座", "tyg_211区_17排08座", "tyg_211区_1排03座", "tyg_211区_6排23座"], "womanSeatName": ["tyg_211区_8排26座", "tyg_211区_7排13座", "tyg_211区_5排15座", "tyg_211区_4排07座", "tyg_211区_1排01座", "tyg_211区_3排14座", "tyg_211区_4排23座", "tyg_211区_16排08座", "tyg_211区_13排02座", "tyg_211区_5排02座", "tyg_211区_7排11座", "tyg_211区_15排03座", "tyg_211区_17排23座", "tyg_211区_6排03座", "tyg_211区_3排08座", "tyg_211区_17排17座"]}, {"areaName": "212", "manNum": 25, "womanNum": 39, "maxNum": 357, "manSeatName": ["tyg_212区_9排03座", "tyg_212区_3排15座", "tyg_212区_7排16座", "tyg_212区_15排14座", "tyg_212区_16排14座", "tyg_212区_3排08座", "tyg_212区_12排02座", "tyg_212区_10排01座", "tyg_212区_16排01座", "tyg_212区_16排15座", "tyg_212区_10排18座", "tyg_212区_12排18座", "tyg_212区_16排17座", "tyg_212区_5排18座", "tyg_212区_4排09座", "tyg_212区_8排12座", "tyg_212区_12排15座", "tyg_212区_9排16座", "tyg_212区_6排01座", "tyg_212区_12排19座", "tyg_212区_17排18座", "tyg_212区_3排02座", "tyg_212区_11排13座", "tyg_212区_4排11座", "tyg_212区_16排19座"], "womanSeatName": ["tyg_212区_16排10座", "tyg_212区_15排20座", "tyg_212区_13排09座", "tyg_212区_12排12座", "tyg_212区_3排12座", "tyg_212区_14排07座", "tyg_212区_8排07座", "tyg_212区_3排20座", "tyg_212区_5排15座", "tyg_212区_3排05座", "tyg_212区_4排15座", "tyg_212区_4排18座", "tyg_212区_14排17座", "tyg_212区_5排19座", "tyg_212区_10排16座", "tyg_212区_5排13座", "tyg_212区_14排14座", "tyg_212区_4排13座", "tyg_212区_17排14座", "tyg_212区_4排20座", "tyg_212区_13排08座", "tyg_212区_7排12座", "tyg_212区_5排05座", "tyg_212区_13排17座", "tyg_212区_3排09座", "tyg_212区_4排19座", "tyg_212区_7排01座", "tyg_212区_7排21座", "tyg_212区_7排10座", "tyg_212区_3排18座", "tyg_212区_6排10座", "tyg_212区_11排12座", "tyg_212区_9排01座", "tyg_212区_2排01座", "tyg_212区_7排14座", "tyg_212区_3排16座", "tyg_212区_6排03座", "tyg_212区_3排14座", "tyg_212区_9排05座"]}, {"areaName": "213", "manNum": 0, "womanNum": 59, "maxNum": 398, "manSeatName": [], "womanSeatName": ["tyg_213区_14排01座", "tyg_213区_15排17座", "tyg_213区_24排15座", "tyg_213区_10排06座", "tyg_213区_11排11座", "tyg_213区_28排10座", "tyg_213区_22排04座", "tyg_213区_27排23座", "tyg_213区_18排09座", "tyg_213区_18排14座", "tyg_213区_21排04座", "tyg_213区_27排06座", "tyg_213区_26排07座", "tyg_213区_16排17座", "tyg_213区_5排03座", "tyg_213区_15排15座", "tyg_213区_28排17座", "tyg_213区_28排24座", "tyg_213区_27排12座", "tyg_213区_19排06座", "tyg_213区_28排22座", "tyg_213区_18排15座", "tyg_213区_14排08座", "tyg_213区_16排14座", "tyg_213区_22排12座", "tyg_213区_14排04座", "tyg_213区_21排06座", "tyg_213区_27排09座", "tyg_213区_20排12座", "tyg_213区_23排01座", "tyg_213区_26排13座", "tyg_213区_23排16座", "tyg_213区_19排13座", "tyg_213区_27排04座", "tyg_213区_14排05座", "tyg_213区_12排11座", "tyg_213区_20排16座", "tyg_213区_14排09座", "tyg_213区_26排21座", "tyg_213区_27排24座", "tyg_213区_27排26座", "tyg_213区_26排09座", "tyg_213区_27排19座", "tyg_213区_15排13座", "tyg_213区_16排01座", "tyg_213区_17排11座", "tyg_213区_18排08座", "tyg_213区_12排08座", "tyg_213区_12排09座", "tyg_213区_15排02座", "tyg_213区_25排04座", "tyg_213区_12排10座", "tyg_213区_6排06座", "tyg_213区_14排11座", "tyg_213区_16排03座", "tyg_213区_3排05座", "tyg_213区_3排02座", "tyg_213区_18排20座", "tyg_213区_16排05座"]}, {"areaName": "214", "manNum": 28, "womanNum": 11, "maxNum": 216, "manSeatName": ["tyg_214区_28排01座", "tyg_214区_27排19座", "tyg_214区_24排13座", "tyg_214区_28排20座", "tyg_214区_25排09座", "tyg_214区_23排04座", "tyg_214区_28排08座", "tyg_214区_25排03座", "tyg_214区_20排06座", "tyg_214区_16排05座", "tyg_214区_17排15座", "tyg_214区_24排07座", "tyg_214区_21排06座", "tyg_214区_26排04座", "tyg_214区_12排02座", "tyg_214区_28排18座", "tyg_214区_20排08座", "tyg_214区_17排12座", "tyg_214区_20排03座", "tyg_214区_22排07座", "tyg_214区_14排01座", "tyg_214区_23排12座", "tyg_214区_23排07座", "tyg_214区_27排06座", "tyg_214区_17排06座", "tyg_214区_26排06座", "tyg_214区_16排06座", "tyg_214区_21排13座"], "womanSeatName": ["tyg_214区_18排17座", "tyg_214区_21排09座", "tyg_214区_18排09座", "tyg_214区_18排11座", "tyg_214区_20排09座", "tyg_214区_22排05座", "tyg_214区_20排12座", "tyg_214区_26排09座", "tyg_214区_16排14座", "tyg_214区_24排08座", "tyg_214区_23排05座"]}, {"areaName": "215", "manNum": 34, "womanNum": 20, "maxNum": 374, "manSeatName": ["tyg_215区_4排06座", "tyg_215区_11排08座", "tyg_215区_22排06座", "tyg_215区_20排12座", "tyg_215区_9排10座", "tyg_215区_1排01座", "tyg_215区_26排06座", "tyg_215区_25排03座", "tyg_215区_26排07座", "tyg_215区_12排08座", "tyg_215区_26排04座", "tyg_215区_28排24座", "tyg_215区_14排01座", "tyg_215区_25排02座", "tyg_215区_20排09座", "tyg_215区_11排12座", "tyg_215区_13排13座", "tyg_215区_3排01座", "tyg_215区_2排01座", "tyg_215区_14排14座", "tyg_215区_27排01座", "tyg_215区_21排14座", "tyg_215区_17排01座", "tyg_215区_26排08座", "tyg_215区_23排08座", "tyg_215区_28排01座", "tyg_215区_19排03座", "tyg_215区_22排05座", "tyg_215区_16排10座", "tyg_215区_14排02座", "tyg_215区_6排02座", "tyg_215区_21排11座", "tyg_215区_23排03座", "tyg_215区_2排03座"], "womanSeatName": ["tyg_215区_20排11座", "tyg_215区_10排10座", "tyg_215区_19排11座", "tyg_215区_10排06座", "tyg_215区_15排15座", "tyg_215区_23排05座", "tyg_215区_27排14座", "tyg_215区_15排01座", "tyg_215区_4排04座", "tyg_215区_15排04座", "tyg_215区_25排08座", "tyg_215区_14排11座", "tyg_215区_15排09座", "tyg_215区_17排09座", "tyg_215区_22排01座", "tyg_215区_28排15座", "tyg_215区_24排04座", "tyg_215区_7排07座", "tyg_215区_17排17座", "tyg_215区_11排09座"]}, {"areaName": "216", "manNum": 18, "womanNum": 48, "maxNum": 686, "manSeatName": ["tyg_216区_5排22座", "tyg_216区_28排09座", "tyg_216区_18排04座", "tyg_216区_7排07座", "tyg_216区_12排21座", "tyg_216区_19排21座", "tyg_216区_11排14座", "tyg_216区_24排10座", "tyg_216区_24排01座", "tyg_216区_1排18座", "tyg_216区_10排21座", "tyg_216区_12排15座", "tyg_216区_5排09座", "tyg_216区_1排16座", "tyg_216区_4排05座", "tyg_216区_17排11座", "tyg_216区_4排24座", "tyg_216区_11排09座"], "womanSeatName": ["tyg_216区_14排21座", "tyg_216区_16排18座", "tyg_216区_23排06座", "tyg_216区_12排13座", "tyg_216区_15排25座", "tyg_216区_2排01座", "tyg_216区_12排20座", "tyg_216区_11排26座", "tyg_216区_10排11座", "tyg_216区_9排24座", "tyg_216区_4排21座", "tyg_216区_5排19座", "tyg_216区_12排26座", "tyg_216区_13排21座", "tyg_216区_24排18座", "tyg_216区_18排20座", "tyg_216区_11排02座", "tyg_216区_17排23座", "tyg_216区_26排07座", "tyg_216区_7排04座", "tyg_216区_18排05座", "tyg_216区_15排10座", "tyg_216区_16排16座", "tyg_216区_14排07座", "tyg_216区_7排25座", "tyg_216区_22排18座", "tyg_216区_21排07座", "tyg_216区_27排07座", "tyg_216区_3排07座", "tyg_216区_4排02座", "tyg_216区_13排15座", "tyg_216区_25排15座", "tyg_216区_9排25座", "tyg_216区_28排19座", "tyg_216区_20排02座", "tyg_216区_27排15座", "tyg_216区_8排04座", "tyg_216区_15排24座", "tyg_216区_26排13座", "tyg_216区_16排20座", "tyg_216区_14排01座", "tyg_216区_6排13座", "tyg_216区_3排22座", "tyg_216区_26排10座", "tyg_216区_12排11座", "tyg_216区_11排12座", "tyg_216区_25排01座", "tyg_216区_18排10座"]}, {"areaName": "217", "manNum": 23, "womanNum": 35, "maxNum": 374, "manSeatName": ["tyg_217区_19排09座", "tyg_217区_14排05座", "tyg_217区_26排06座", "tyg_217区_19排12座", "tyg_217区_17排17座", "tyg_217区_27排23座", "tyg_217区_22排12座", "tyg_217区_9排02座", "tyg_217区_7排02座", "tyg_217区_5排03座", "tyg_217区_10排08座", "tyg_217区_18排01座", "tyg_217区_11排09座", "tyg_217区_13排12座", "tyg_217区_25排04座", "tyg_217区_25排07座", "tyg_217区_16排15座", "tyg_217区_18排17座", "tyg_217区_19排03座", "tyg_217区_23排10座", "tyg_217区_10排09座", "tyg_217区_19排05座", "tyg_217区_7排06座"], "womanSeatName": ["tyg_217区_15排06座", "tyg_217区_5排06座", "tyg_217区_23排05座", "tyg_217区_24排08座", "tyg_217区_18排04座", "tyg_217区_26排08座", "tyg_217区_25排09座", "tyg_217区_22排05座", "tyg_217区_12排03座", "tyg_217区_19排10座", "tyg_217区_10排06座", "tyg_217区_11排12座", "tyg_217区_13排07座", "tyg_217区_18排02座", "tyg_217区_10排10座", "tyg_217区_24排14座", "tyg_217区_14排14座", "tyg_217区_26排18座", "tyg_217区_15排12座", "tyg_217区_1排04座", "tyg_217区_21排06座", "tyg_217区_15排08座", "tyg_217区_26排15座", "tyg_217区_18排06座", "tyg_217区_14排07座", "tyg_217区_14排10座", "tyg_217区_22排14座", "tyg_217区_5排02座", "tyg_217区_16排14座", "tyg_217区_16排11座", "tyg_217区_23排14座", "tyg_217区_26排14座", "tyg_217区_22排06座", "tyg_217区_2排01座", "tyg_217区_24排12座"]}, {"areaName": "218", "manNum": 11, "womanNum": 39, "maxNum": 140, "manSeatName": ["tyg_218区_18排14座", "tyg_218区_22排11座", "tyg_218区_19排01座", "tyg_218区_19排03座", "tyg_218区_18排15座", "tyg_218区_13排01座", "tyg_218区_20排05座", "tyg_218区_19排06座", "tyg_218区_16排12座", "tyg_218区_20排02座", "tyg_218区_19排12座"], "womanSeatName": ["tyg_218区_23排09座", "tyg_218区_17排15座", "tyg_218区_24排05座", "tyg_218区_24排12座", "tyg_218区_23排01座", "tyg_218区_21排13座", "tyg_218区_24排06座", "tyg_218区_24排14座", "tyg_218区_17排03座", "tyg_218区_17排09座", "tyg_218区_14排01座", "tyg_218区_24排09座", "tyg_218区_22排04座", "tyg_218区_17排05座", "tyg_218区_15排02座", "tyg_218区_24排02座", "tyg_218区_20排11座", "tyg_218区_10排01座", "tyg_218区_21排08座", "tyg_218区_21排05座", "tyg_218区_24排11座", "tyg_218区_17排10座", "tyg_218区_17排01座", "tyg_218区_23排06座", "tyg_218区_23排04座", "tyg_218区_19排09座", "tyg_218区_23排14座", "tyg_218区_20排10座", "tyg_218区_16排02座", "tyg_218区_21排01座", "tyg_218区_16排13座", "tyg_218区_20排08座", "tyg_218区_23排13座", "tyg_218区_22排12座", "tyg_218区_23排03座", "tyg_218区_13排02座", "tyg_218区_23排10座", "tyg_218区_18排17座", "tyg_218区_19排07座"]}, {"areaName": "219", "manNum": 56, "womanNum": 9, "maxNum": 389, "manSeatName": ["tyg_219区_27排05座", "tyg_219区_2排04座", "tyg_219区_14排14座", "tyg_219区_17排05座", "tyg_219区_19排04座", "tyg_219区_27排23座", "tyg_219区_27排20座", "tyg_219区_28排06座", "tyg_219区_18排03座", "tyg_219区_16排06座", "tyg_219区_19排14座", "tyg_219区_22排14座", "tyg_219区_9排04座", "tyg_219区_25排02座", "tyg_219区_28排05座", "tyg_219区_20排07座", "tyg_219区_9排06座", "tyg_219区_23排14座", "tyg_219区_19排03座", "tyg_219区_17排04座", "tyg_219区_17排10座", "tyg_219区_27排08座", "tyg_219区_7排05座", "tyg_219区_16排15座", "tyg_219区_27排14座", "tyg_219区_25排08座", "tyg_219区_16排14座", "tyg_219区_13排07座", "tyg_219区_4排04座", "tyg_219区_26排08座", "tyg_219区_23排06座", "tyg_219区_16排12座", "tyg_219区_28排15座", "tyg_219区_18排13座", "tyg_219区_1排02座", "tyg_219区_18排20座", "tyg_219区_11排06座", "tyg_219区_28排25座", "tyg_219区_20排02座", "tyg_219区_16排17座", "tyg_219区_18排09座", "tyg_219区_28排23座", "tyg_219区_11排11座", "tyg_219区_15排08座", "tyg_219区_24排12座", "tyg_219区_24排18座", "tyg_219区_21排13座", "tyg_219区_28排07座", "tyg_219区_20排10座", "tyg_219区_5排04座", "tyg_219区_26排07座", "tyg_219区_19排13座", "tyg_219区_15排01座", "tyg_219区_22排05座", "tyg_219区_17排12座", "tyg_219区_11排04座"], "womanSeatName": ["tyg_219区_28排01座", "tyg_219区_24排04座", "tyg_219区_3排05座", "tyg_219区_26排02座", "tyg_219区_5排03座", "tyg_219区_13排10座", "tyg_219区_18排01座", "tyg_219区_12排10座", "tyg_219区_24排13座"]}, {"areaName": "220", "manNum": 33, "womanNum": 15, "maxNum": 489, "manSeatName": ["tyg_220区_2排14座", "tyg_220区_27排03座", "tyg_220区_7排05座", "tyg_220区_3排19座", "tyg_220区_13排10座", "tyg_220区_16排21座", "tyg_220区_15排11座", "tyg_220区_28排10座", "tyg_220区_4排04座", "tyg_220区_2排07座", "tyg_220区_1排11座", "tyg_220区_12排09座", "tyg_220区_16排14座", "tyg_220区_11排18座", "tyg_220区_3排14座", "tyg_220区_23排04座", "tyg_220区_18排02座", "tyg_220区_5排16座", "tyg_220区_6排18座", "tyg_220区_28排01座", "tyg_220区_3排04座", "tyg_220区_14排14座", "tyg_220区_6排02座", "tyg_220区_9排16座", "tyg_220区_14排05座", "tyg_220区_27排13座", "tyg_220区_13排16座", "tyg_220区_6排09座", "tyg_220区_23排02座", "tyg_220区_18排21座", "tyg_220区_12排08座", "tyg_220区_17排17座", "tyg_220区_7排11座"], "womanSeatName": ["tyg_220区_1排19座", "tyg_220区_6排12座", "tyg_220区_10排12座", "tyg_220区_6排11座", "tyg_220区_17排16座", "tyg_220区_14排09座", "tyg_220区_12排17座", "tyg_220区_17排14座", "tyg_220区_17排03座", "tyg_220区_13排05座", "tyg_220区_4排20座", "tyg_220区_11排05座", "tyg_220区_24排01座", "tyg_220区_16排09座", "tyg_220区_17排20座"]}, {"areaName": "501", "manNum": 26, "womanNum": 29, "maxNum": 258, "manSeatName": ["tyg_501区_9排09座", "tyg_501区_8排02座", "tyg_501区_2排19座", "tyg_501区_11排02座", "tyg_501区_9排21座", "tyg_501区_3排03座", "tyg_501区_10排21座", "tyg_501区_10排06座", "tyg_501区_2排08座", "tyg_501区_2排24座", "tyg_501区_6排04座", "tyg_501区_1排03座", "tyg_501区_3排14座", "tyg_501区_10排04座", "tyg_501区_10排16座", "tyg_501区_1排10座", "tyg_501区_1排17座", "tyg_501区_5排02座", "tyg_501区_7排01座", "tyg_501区_8排07座", "tyg_501区_9排22座", "tyg_501区_4排08座", "tyg_501区_8排09座", "tyg_501区_8排15座", "tyg_501区_5排18座", "tyg_501区_2排17座"], "womanSeatName": ["tyg_501区_11排20座", "tyg_501区_4排21座", "tyg_501区_3排02座", "tyg_501区_6排12座", "tyg_501区_5排04座", "tyg_501区_9排25座", "tyg_501区_7排04座", "tyg_501区_1排16座", "tyg_501区_4排02座", "tyg_501区_7排16座", "tyg_501区_6排03座", "tyg_501区_10排08座", "tyg_501区_9排10座", "tyg_501区_6排17座", "tyg_501区_2排22座", "tyg_501区_3排20座", "tyg_501区_4排09座", "tyg_501区_4排03座", "tyg_501区_2排13座", "tyg_501区_4排06座", "tyg_501区_1排15座", "tyg_501区_2排23座", "tyg_501区_8排06座", "tyg_501区_10排22座", "tyg_501区_4排15座", "tyg_501区_1排13座", "tyg_501区_2排02座", "tyg_501区_4排05座", "tyg_501区_3排10座"]}, {"areaName": "502", "manNum": 12, "womanNum": 40, "maxNum": 195, "manSeatName": ["tyg_502区_1排13座", "tyg_502区_5排09座", "tyg_502区_9排01座", "tyg_502区_4排01座", "tyg_502区_10排20座", "tyg_502区_11排06座", "tyg_502区_9排12座", "tyg_502区_6排13座", "tyg_502区_2排16座", "tyg_502区_4排04座", "tyg_502区_10排09座", "tyg_502区_7排08座"], "womanSeatName": ["tyg_502区_11排12座", "tyg_502区_10排19座", "tyg_502区_11排18座", "tyg_502区_5排05座", "tyg_502区_7排02座", "tyg_502区_2排12座", "tyg_502区_9排06座", "tyg_502区_7排10座", "tyg_502区_11排09座", "tyg_502区_2排17座", "tyg_502区_9排21座", "tyg_502区_3排13座", "tyg_502区_5排13座", "tyg_502区_1排05座", "tyg_502区_9排19座", "tyg_502区_11排10座", "tyg_502区_3排02座", "tyg_502区_5排06座", "tyg_502区_9排17座", "tyg_502区_11排03座", "tyg_502区_3排01座", "tyg_502区_1排12座", "tyg_502区_8排15座", "tyg_502区_1排03座", "tyg_502区_11排01座", "tyg_502区_3排06座", "tyg_502区_10排17座", "tyg_502区_10排10座", "tyg_502区_2排07座", "tyg_502区_2排19座", "tyg_502区_10排13座", "tyg_502区_2排14座", "tyg_502区_11排04座", "tyg_502区_2排03座", "tyg_502区_1排09座", "tyg_502区_11排05座", "tyg_502区_3排04座", "tyg_502区_8排02座", "tyg_502区_10排07座", "tyg_502区_6排15座"]}, {"areaName": "503", "manNum": 21, "womanNum": 24, "maxNum": 374, "manSeatName": ["tyg_503区_11排08座", "tyg_503区_2排24座", "tyg_503区_4排09座", "tyg_503区_10排17座", "tyg_503区_11排41座", "tyg_503区_5排08座", "tyg_503区_9排11座", "tyg_503区_9排27座", "tyg_503区_6排23座", "tyg_503区_1排05座", "tyg_503区_1排01座", "tyg_503区_6排20座", "tyg_503区_6排11座", "tyg_503区_4排12座", "tyg_503区_11排09座", "tyg_503区_6排22座", "tyg_503区_5排23座", "tyg_503区_7排12座", "tyg_503区_4排21座", "tyg_503区_1排14座", "tyg_503区_6排29座"], "womanSeatName": ["tyg_503区_1排10座", "tyg_503区_11排04座", "tyg_503区_11排15座", "tyg_503区_6排28座", "tyg_503区_4排25座", "tyg_503区_9排12座", "tyg_503区_9排14座", "tyg_503区_5排07座", "tyg_503区_5排27座", "tyg_503区_8排11座", "tyg_503区_6排07座", "tyg_503区_8排29座", "tyg_503区_8排25座", "tyg_503区_9排40座", "tyg_503区_1排07座", "tyg_503区_1排28座", "tyg_503区_7排10座", "tyg_503区_2排31座", "tyg_503区_5排09座", "tyg_503区_1排12座", "tyg_503区_10排14座", "tyg_503区_8排19座", "tyg_503区_9排34座", "tyg_503区_6排32座"]}, {"areaName": "504", "manNum": 17, "womanNum": 25, "maxNum": 255, "manSeatName": ["tyg_504区_4排10座", "tyg_504区_6排13座", "tyg_504区_9排21座", "tyg_504区_6排03座", "tyg_504区_4排18座", "tyg_504区_5排21座", "tyg_504区_4排20座", "tyg_504区_2排22座", "tyg_504区_2排18座", "tyg_504区_2排14座", "tyg_504区_1排23座", "tyg_504区_3排01座", "tyg_504区_4排13座", "tyg_504区_1排07座", "tyg_504区_8排09座", "tyg_504区_9排20座", "tyg_504区_6排15座"], "womanSeatName": ["tyg_504区_7排15座", "tyg_504区_9排08座", "tyg_504区_1排22座", "tyg_504区_10排16座", "tyg_504区_1排16座", "tyg_504区_4排12座", "tyg_504区_10排05座", "tyg_504区_11排05座", "tyg_504区_4排19座", "tyg_504区_5排12座", "tyg_504区_8排04座", "tyg_504区_7排18座", "tyg_504区_11排13座", "tyg_504区_1排09座", "tyg_504区_3排05座", "tyg_504区_8排06座", "tyg_504区_8排01座", "tyg_504区_9排13座", "tyg_504区_10排19座", "tyg_504区_9排14座", "tyg_504区_8排15座", "tyg_504区_10排08座", "tyg_504区_11排18座", "tyg_504区_4排17座", "tyg_504区_3排15座"]}, {"areaName": "505", "manNum": 20, "womanNum": 35, "maxNum": 341, "manSeatName": ["tyg_505区_7排27座", "tyg_505区_8排04座", "tyg_505区_5排15座", "tyg_505区_7排04座", "tyg_505区_6排21座", "tyg_505区_3排08座", "tyg_505区_6排08座", "tyg_505区_6排05座", "tyg_505区_2排31座", "tyg_505区_6排28座", "tyg_505区_10排06座", "tyg_505区_2排15座", "tyg_505区_5排02座", "tyg_505区_4排26座", "tyg_505区_5排07座", "tyg_505区_8排17座", "tyg_505区_7排19座", "tyg_505区_11排17座", "tyg_505区_11排33座", "tyg_505区_2排24座"], "womanSeatName": ["tyg_505区_4排12座", "tyg_505区_6排07座", "tyg_505区_8排07座", "tyg_505区_3排03座", "tyg_505区_3排12座", "tyg_505区_6排24座", "tyg_505区_9排31座", "tyg_505区_2排21座", "tyg_505区_2排20座", "tyg_505区_1排23座", "tyg_505区_8排10座", "tyg_505区_8排12座", "tyg_505区_6排19座", "tyg_505区_8排24座", "tyg_505区_10排21座", "tyg_505区_10排31座", "tyg_505区_6排06座", "tyg_505区_5排21座", "tyg_505区_10排32座", "tyg_505区_8排21座", "tyg_505区_9排19座", "tyg_505区_8排01座", "tyg_505区_7排25座", "tyg_505区_9排10座", "tyg_505区_11排32座", "tyg_505区_1排28座", "tyg_505区_1排26座", "tyg_505区_7排06座", "tyg_505区_9排08座", "tyg_505区_1排21座", "tyg_505区_1排31座", "tyg_505区_10排15座", "tyg_505区_10排16座", "tyg_505区_4排02座", "tyg_505区_3排26座"]}, {"areaName": "506", "manNum": 23, "womanNum": 22, "maxNum": 156, "manSeatName": ["tyg_506区_1排10座", "tyg_506区_6排10座", "tyg_506区_2排18座", "tyg_506区_4排17座", "tyg_506区_6排13座", "tyg_506区_3排04座", "tyg_506区_6排17座", "tyg_506区_4排03座", "tyg_506区_4排07座", "tyg_506区_7排13座", "tyg_506区_6排21座", "tyg_506区_4排02座", "tyg_506区_2排12座", "tyg_506区_1排08座", "tyg_506区_7排02座", "tyg_506区_2排13座", "tyg_506区_7排06座", "tyg_506区_4排21座", "tyg_506区_5排14座", "tyg_506区_6排19座", "tyg_506区_2排25座", "tyg_506区_1排09座", "tyg_506区_7排17座"], "womanSeatName": ["tyg_506区_1排12座", "tyg_506区_2排16座", "tyg_506区_1排17座", "tyg_506区_3排08座", "tyg_506区_4排12座", "tyg_506区_7排05座", "tyg_506区_4排13座", "tyg_506区_6排05座", "tyg_506区_5排05座", "tyg_506区_1排19座", "tyg_506区_6排09座", "tyg_506区_1排22座", "tyg_506区_3排15座", "tyg_506区_6排02座", "tyg_506区_7排15座", "tyg_506区_6排18座", "tyg_506区_5排08座", "tyg_506区_5排02座", "tyg_506区_4排16座", "tyg_506区_6排12座", "tyg_506区_5排07座", "tyg_506区_1排25座"]}, {"areaName": "507", "manNum": 17, "womanNum": 37, "maxNum": 341, "manSeatName": ["tyg_507区_6排18座", "tyg_507区_1排06座", "tyg_507区_5排09座", "tyg_507区_11排18座", "tyg_507区_10排15座", "tyg_507区_11排06座", "tyg_507区_1排07座", "tyg_507区_2排27座", "tyg_507区_9排16座", "tyg_507区_5排01座", "tyg_507区_10排08座", "tyg_507区_6排16座", "tyg_507区_5排06座", "tyg_507区_5排12座", "tyg_507区_6排27座", "tyg_507区_10排28座", "tyg_507区_7排02座"], "womanSeatName": ["tyg_507区_9排13座", "tyg_507区_7排06座", "tyg_507区_1排22座", "tyg_507区_5排28座", "tyg_507区_4排12座", "tyg_507区_10排22座", "tyg_507区_7排07座", "tyg_507区_11排11座", "tyg_507区_7排17座", "tyg_507区_9排23座", "tyg_507区_6排28座", "tyg_507区_3排16座", "tyg_507区_11排27座", "tyg_507区_10排13座", "tyg_507区_10排14座", "tyg_507区_2排12座", "tyg_507区_1排21座", "tyg_507区_3排06座", "tyg_507区_10排36座", "tyg_507区_5排02座", "tyg_507区_10排24座", "tyg_507区_8排05座", "tyg_507区_6排15座", "tyg_507区_11排05座", "tyg_507区_10排06座", "tyg_507区_10排12座", "tyg_507区_8排22座", "tyg_507区_9排09座", "tyg_507区_9排35座", "tyg_507区_10排09座", "tyg_507区_8排08座", "tyg_507区_7排08座", "tyg_507区_1排20座", "tyg_507区_1排11座", "tyg_507区_8排13座", "tyg_507区_11排03座", "tyg_507区_11排10座"]}, {"areaName": "508", "manNum": 48, "womanNum": 5, "maxNum": 255, "manSeatName": ["tyg_508区_2排05座", "tyg_508区_1排25座", "tyg_508区_1排01座", "tyg_508区_2排25座", "tyg_508区_1排19座", "tyg_508区_6排07座", "tyg_508区_8排08座", "tyg_508区_9排22座", "tyg_508区_3排05座", "tyg_508区_8排05座", "tyg_508区_10排06座", "tyg_508区_8排17座", "tyg_508区_7排11座", "tyg_508区_4排20座", "tyg_508区_4排11座", "tyg_508区_11排07座", "tyg_508区_4排18座", "tyg_508区_3排12座", "tyg_508区_2排21座", "tyg_508区_10排26座", "tyg_508区_11排06座", "tyg_508区_7排03座", "tyg_508区_7排05座", "tyg_508区_8排04座", "tyg_508区_2排20座", "tyg_508区_6排01座", "tyg_508区_9排20座", "tyg_508区_9排05座", "tyg_508区_3排02座", "tyg_508区_5排04座", "tyg_508区_3排16座", "tyg_508区_6排11座", "tyg_508区_9排16座", "tyg_508区_10排11座", "tyg_508区_10排13座", "tyg_508区_11排24座", "tyg_508区_4排15座", "tyg_508区_8排01座", "tyg_508区_4排12座", "tyg_508区_7排17座", "tyg_508区_2排06座", "tyg_508区_10排17座", "tyg_508区_2排02座", "tyg_508区_6排04座", "tyg_508区_10排22座", "tyg_508区_3排15座", "tyg_508区_2排26座", "tyg_508区_8排16座"], "womanSeatName": ["tyg_508区_5排12座", "tyg_508区_5排10座", "tyg_508区_6排10座", "tyg_508区_3排08座", "tyg_508区_11排23座"]}, {"areaName": "509", "manNum": 48, "womanNum": 3, "maxNum": 375, "manSeatName": ["tyg_509区_8排10座", "tyg_509区_11排07座", "tyg_509区_5排04座", "tyg_509区_10排30座", "tyg_509区_3排20座", "tyg_509区_11排37座", "tyg_509区_8排19座", "tyg_509区_9排05座", "tyg_509区_7排23座", "tyg_509区_2排17座", "tyg_509区_1排07座", "tyg_509区_3排17座", "tyg_509区_2排04座", "tyg_509区_1排06座", "tyg_509区_8排27座", "tyg_509区_7排25座", "tyg_509区_9排37座", "tyg_509区_3排23座", "tyg_509区_11排40座", "tyg_509区_11排01座", "tyg_509区_5排27座", "tyg_509区_7排32座", "tyg_509区_1排30座", "tyg_509区_8排28座", "tyg_509区_1排19座", "tyg_509区_2排29座", "tyg_509区_11排36座", "tyg_509区_2排12座", "tyg_509区_4排23座", "tyg_509区_2排30座", "tyg_509区_1排18座", "tyg_509区_4排19座", "tyg_509区_3排22座", "tyg_509区_2排07座", "tyg_509区_10排04座", "tyg_509区_4排18座", "tyg_509区_5排03座", "tyg_509区_2排05座", "tyg_509区_11排08座", "tyg_509区_7排14座", "tyg_509区_9排06座", "tyg_509区_4排05座", "tyg_509区_10排27座", "tyg_509区_3排18座", "tyg_509区_7排27座", "tyg_509区_4排26座", "tyg_509区_5排07座", "tyg_509区_9排38座"], "womanSeatName": ["tyg_509区_4排28座", "tyg_509区_8排31座", "tyg_509区_9排21座"]}, {"areaName": "510", "manNum": 32, "womanNum": 20, "maxNum": 195, "manSeatName": ["tyg_510区_1排16座", "tyg_510区_3排15座", "tyg_510区_2排21座", "tyg_510区_9排04座", "tyg_510区_5排09座", "tyg_510区_3排03座", "tyg_510区_8排06座", "tyg_510区_3排04座", "tyg_510区_7排08座", "tyg_510区_11排16座", "tyg_510区_6排01座", "tyg_510区_3排01座", "tyg_510区_8排04座", "tyg_510区_1排07座", "tyg_510区_8排03座", "tyg_510区_9排05座", "tyg_510区_1排12座", "tyg_510区_10排06座", "tyg_510区_9排10座", "tyg_510区_1排20座", "tyg_510区_2排05座", "tyg_510区_5排13座", "tyg_510区_7排12座", "tyg_510区_6排13座", "tyg_510区_11排10座", "tyg_510区_9排11座", "tyg_510区_10排09座", "tyg_510区_4排13座", "tyg_510区_4排12座", "tyg_510区_4排04座", "tyg_510区_4排07座", "tyg_510区_10排11座"], "womanSeatName": ["tyg_510区_9排02座", "tyg_510区_10排08座", "tyg_510区_7排03座", "tyg_510区_11排12座", "tyg_510区_11排14座", "tyg_510区_7排04座", "tyg_510区_7排14座", "tyg_510区_5排11座", "tyg_510区_10排03座", "tyg_510区_4排03座", "tyg_510区_1排11座", "tyg_510区_5排03座", "tyg_510区_7排10座", "tyg_510区_2排09座", "tyg_510区_10排20座", "tyg_510区_8排15座", "tyg_510区_8排01座", "tyg_510区_11排04座", "tyg_510区_2排12座", "tyg_510区_8排11座"]}, {"areaName": "511", "manNum": 16, "womanNum": 37, "maxNum": 239, "manSeatName": ["tyg_511区_10排12座", "tyg_511区_3排03座", "tyg_511区_10排15座", "tyg_511区_8排16座", "tyg_511区_10排06座", "tyg_511区_1排24座", "tyg_511区_10排23座", "tyg_511区_7排16座", "tyg_511区_5排01座", "tyg_511区_9排21座", "tyg_511区_1排16座", "tyg_511区_5排16座", "tyg_511区_11排11座", "tyg_511区_5排03座", "tyg_511区_1排15座", "tyg_511区_11排21座"], "womanSeatName": ["tyg_511区_5排06座", "tyg_511区_10排04座", "tyg_511区_5排05座", "tyg_511区_10排03座", "tyg_511区_4排03座", "tyg_511区_1排25座", "tyg_511区_10排11座", "tyg_511区_9排17座", "tyg_511区_4排13座", "tyg_511区_8排02座", "tyg_511区_10排05座", "tyg_511区_7排18座", "tyg_511区_11排01座", "tyg_511区_3排04座", "tyg_511区_7排12座", "tyg_511区_8排15座", "tyg_511区_11排20座", "tyg_511区_11排03座", "tyg_511区_9排22座", "tyg_511区_3排07座", "tyg_511区_4排01座", "tyg_511区_9排23座", "tyg_511区_4排06座", "tyg_511区_3排10座", "tyg_511区_6排06座", "tyg_511区_11排04座", "tyg_511区_7排10座", "tyg_511区_4排09座", "tyg_511区_2排11座", "tyg_511区_9排08座", "tyg_511区_2排07座", "tyg_511区_7排06座", "tyg_511区_8排01座", "tyg_511区_7排11座", "tyg_511区_6排12座", "tyg_511区_8排11座", "tyg_511区_8排05座"]}, {"areaName": "512", "manNum": 10, "womanNum": 49, "maxNum": 189, "manSeatName": ["tyg_512区_9排15座", "tyg_512区_1排09座", "tyg_512区_7排06座", "tyg_512区_9排17座", "tyg_512区_12排10座", "tyg_512区_2排03座", "tyg_512区_9排13座", "tyg_512区_2排15座", "tyg_512区_2排10座", "tyg_512区_7排03座"], "womanSeatName": ["tyg_512区_1排11座", "tyg_512区_1排03座", "tyg_512区_8排14座", "tyg_512区_7排11座", "tyg_512区_10排06座", "tyg_512区_10排14座", "tyg_512区_6排09座", "tyg_512区_9排19座", "tyg_512区_7排12座", "tyg_512区_8排09座", "tyg_512区_9排04座", "tyg_512区_2排13座", "tyg_512区_1排04座", "tyg_512区_2排07座", "tyg_512区_12排11座", "tyg_512区_6排01座", "tyg_512区_1排20座", "tyg_512区_2排06座", "tyg_512区_4排09座", "tyg_512区_3排06座", "tyg_512区_7排02座", "tyg_512区_5排08座", "tyg_512区_5排10座", "tyg_512区_9排14座", "tyg_512区_3排12座", "tyg_512区_8排10座", "tyg_512区_2排21座", "tyg_512区_4排06座", "tyg_512区_9排07座", "tyg_512区_10排05座", "tyg_512区_12排09座", "tyg_512区_10排15座", "tyg_512区_10排04座", "tyg_512区_12排02座", "tyg_512区_1排18座", "tyg_512区_6排04座", "tyg_512区_4排12座", "tyg_512区_1排12座", "tyg_512区_1排13座", "tyg_512区_9排02座", "tyg_512区_1排14座", "tyg_512区_10排17座", "tyg_512区_10排03座", "tyg_512区_3排01座", "tyg_512区_10排02座", "tyg_512区_2排11座", "tyg_512区_1排19座", "tyg_512区_9排05座", "tyg_512区_5排02座"]}, {"areaName": "513", "manNum": 22, "womanNum": 36, "maxNum": 373, "manSeatName": ["tyg_513区_7排24座", "tyg_513区_8排18座", "tyg_513区_2排08座", "tyg_513区_10排39座", "tyg_513区_9排40座", "tyg_513区_2排10座", "tyg_513区_11排34座", "tyg_513区_3排16座", "tyg_513区_2排34座", "tyg_513区_6排11座", "tyg_513区_7排29座", "tyg_513区_11排19座", "tyg_513区_8排02座", "tyg_513区_5排29座", "tyg_513区_3排06座", "tyg_513区_11排18座", "tyg_513区_1排19座", "tyg_513区_1排25座", "tyg_513区_7排21座", "tyg_513区_2排23座", "tyg_513区_4排12座", "tyg_513区_11排02座"], "womanSeatName": ["tyg_513区_1排18座", "tyg_513区_9排12座", "tyg_513区_9排02座", "tyg_513区_10排07座", "tyg_513区_11排05座", "tyg_513区_10排04座", "tyg_513区_9排26座", "tyg_513区_8排30座", "tyg_513区_4排02座", "tyg_513区_11排21座", "tyg_513区_9排11座", "tyg_513区_7排08座", "tyg_513区_2排02座", "tyg_513区_2排19座", "tyg_513区_5排06座", "tyg_513区_8排24座", "tyg_513区_3排28座", "tyg_513区_10排01座", "tyg_513区_10排16座", "tyg_513区_7排17座", "tyg_513区_3排22座", "tyg_513区_11排26座", "tyg_513区_2排24座", "tyg_513区_5排15座", "tyg_513区_4排08座", "tyg_513区_10排24座", "tyg_513区_9排27座", "tyg_513区_11排32座", "tyg_513区_5排22座", "tyg_513区_2排18座", "tyg_513区_6排08座", "tyg_513区_5排04座", "tyg_513区_9排14座", "tyg_513区_1排04座", "tyg_513区_8排33座", "tyg_513区_4排18座"]}, {"areaName": "514", "manNum": 24, "womanNum": 33, "maxNum": 255, "manSeatName": ["tyg_514区_4排06座", "tyg_514区_3排06座", "tyg_514区_10排10座", "tyg_514区_9排03座", "tyg_514区_4排05座", "tyg_514区_1排19座", "tyg_514区_11排15座", "tyg_514区_11排05座", "tyg_514区_11排08座", "tyg_514区_8排16座", "tyg_514区_10排19座", "tyg_514区_3排05座", "tyg_514区_9排23座", "tyg_514区_1排10座", "tyg_514区_7排03座", "tyg_514区_7排21座", "tyg_514区_9排24座", "tyg_514区_8排01座", "tyg_514区_3排12座", "tyg_514区_6排14座", "tyg_514区_5排11座", "tyg_514区_9排22座", "tyg_514区_11排10座", "tyg_514区_11排02座"], "womanSeatName": ["tyg_514区_2排15座", "tyg_514区_11排22座", "tyg_514区_2排16座", "tyg_514区_10排01座", "tyg_514区_5排05座", "tyg_514区_8排04座", "tyg_514区_10排26座", "tyg_514区_11排06座", "tyg_514区_2排14座", "tyg_514区_9排04座", "tyg_514区_2排11座", "tyg_514区_3排01座", "tyg_514区_4排17座", "tyg_514区_9排17座", "tyg_514区_1排13座", "tyg_514区_7排05座", "tyg_514区_5排17座", "tyg_514区_10排17座", "tyg_514区_1排21座", "tyg_514区_1排20座", "tyg_514区_11排01座", "tyg_514区_4排13座", "tyg_514区_11排04座", "tyg_514区_8排10座", "tyg_514区_4排18座", "tyg_514区_10排22座", "tyg_514区_5排04座", "tyg_514区_9排13座", "tyg_514区_4排02座", "tyg_514区_2排19座", "tyg_514区_7排04座", "tyg_514区_1排25座", "tyg_514区_7排10座"]}, {"areaName": "515", "manNum": 32, "womanNum": 12, "maxNum": 341, "manSeatName": ["tyg_515区_9排35座", "tyg_515区_1排29座", "tyg_515区_7排17座", "tyg_515区_1排07座", "tyg_515区_6排06座", "tyg_515区_10排35座", "tyg_515区_7排26座", "tyg_515区_10排33座", "tyg_515区_4排07座", "tyg_515区_4排03座", "tyg_515区_1排23座", "tyg_515区_3排02座", "tyg_515区_4排09座", "tyg_515区_8排11座", "tyg_515区_2排20座", "tyg_515区_6排03座", "tyg_515区_6排12座", "tyg_515区_5排19座", "tyg_515区_6排09座", "tyg_515区_9排17座", "tyg_515区_10排26座", "tyg_515区_10排24座", "tyg_515区_6排13座", "tyg_515区_5排16座", "tyg_515区_9排25座", "tyg_515区_8排23座", "tyg_515区_5排04座", "tyg_515区_6排07座", "tyg_515区_2排16座", "tyg_515区_10排25座", "tyg_515区_7排04座", "tyg_515区_2排15座"], "womanSeatName": ["tyg_515区_3排09座", "tyg_515区_1排10座", "tyg_515区_8排16座", "tyg_515区_6排28座", "tyg_515区_1排04座", "tyg_515区_10排18座", "tyg_515区_6排22座", "tyg_515区_11排10座", "tyg_515区_1排24座", "tyg_515区_7排20座", "tyg_515区_8排30座", "tyg_515区_3排11座"]}, {"areaName": "516", "manNum": 42, "womanNum": 11, "maxNum": 154, "manSeatName": ["tyg_516区_2排11座", "tyg_516区_6排09座", "tyg_516区_2排09座", "tyg_516区_2排16座", "tyg_516区_7排09座", "tyg_516区_6排05座", "tyg_516区_1排12座", "tyg_516区_1排05座", "tyg_516区_3排17座", "tyg_516区_7排18座", "tyg_516区_6排10座", "tyg_516区_1排24座", "tyg_516区_4排21座", "tyg_516区_1排08座", "tyg_516区_6排18座", "tyg_516区_2排07座", "tyg_516区_4排16座", "tyg_516区_2排18座", "tyg_516区_1排07座", "tyg_516区_6排03座", "tyg_516区_6排19座", "tyg_516区_6排12座", "tyg_516区_5排06座", "tyg_516区_7排07座", "tyg_516区_4排11座", "tyg_516区_2排20座", "tyg_516区_1排17座", "tyg_516区_7排13座", "tyg_516区_4排09座", "tyg_516区_4排13座", "tyg_516区_5排18座", "tyg_516区_6排17座", "tyg_516区_3排04座", "tyg_516区_7排06座", "tyg_516区_7排14座", "tyg_516区_3排09座", "tyg_516区_7排03座", "tyg_516区_3排12座", "tyg_516区_2排17座", "tyg_516区_3排03座", "tyg_516区_4排17座", "tyg_516区_2排23座"], "womanSeatName": ["tyg_516区_2排19座", "tyg_516区_1排02座", "tyg_516区_1排03座", "tyg_516区_4排10座", "tyg_516区_2排03座", "tyg_516区_4排07座", "tyg_516区_5排19座", "tyg_516区_3排16座", "tyg_516区_4排18座", "tyg_516区_2排04座", "tyg_516区_5排04座"]}, {"areaName": "517", "manNum": 20, "womanNum": 34, "maxNum": 341, "manSeatName": ["tyg_517区1排19座", "tyg_517区3排21座", "tyg_517区_9排18座", "tyg_517区_11排09座", "tyg_517区1排04座", "tyg_517区_9排06座", "tyg_517区_7排26座", "tyg_517区4排16座", "tyg_517区4排25座", "tyg_517区_6排22座", "tyg_517区5排08座", "tyg_517区3排09座", "tyg_517区_11排05座", "tyg_517区2排23座", "tyg_517区3排19座", "tyg_517区_9排08座", "tyg_517区_11排04座", "tyg_517区_6排20座", "tyg_517区5排07座", "tyg_517区_8排23座"], "womanSeatName": ["tyg_517区5排27座", "tyg_517区1排02座", "tyg_517区_9排21座", "tyg_517区_7排05座", "tyg_517区5排01座", "tyg_517区_8排16座", "tyg_517区_6排21座", "tyg_517区5排25座", "tyg_517区4排06座", "tyg_517区_9排09座", "tyg_517区5排21座", "tyg_517区_6排07座", "tyg_517区_10排02座", "tyg_517区_11排18座", "tyg_517区_8排29座", "tyg_517区_9排14座", "tyg_517区3排16座", "tyg_517区_7排07座", "tyg_517区_7排14座", "tyg_517区_7排21座", "tyg_517区_9排11座", "tyg_517区2排18座", "tyg_517区_7排30座", "tyg_517区1排25座", "tyg_517区_7排08座", "tyg_517区_11排17座", "tyg_517区2排28座", "tyg_517区5排18座", "tyg_517区_6排01座", "tyg_517区2排22座", "tyg_517区4排20座", "tyg_517区_11排33座", "tyg_517区4排21座", "tyg_517区_8排25座"]}, {"areaName": "518", "manNum": 34, "womanNum": 15, "maxNum": 256, "manSeatName": ["tyg_518区3排09座", "tyg_518区4排08座", "tyg_518区8排10座", "tyg_518区10排23座", "tyg_518区2排22座", "tyg_518区10排13座", "tyg_518区4排02座", "tyg_518区11排14座", "tyg_518区11排12座", "tyg_518区4排16座", "tyg_518区1排06座", "tyg_518区2排24座", "tyg_518区1排02座", "tyg_518区6排15座", "tyg_518区11排05座", "tyg_518区1排01座", "tyg_518区1排09座", "tyg_518区8排14座", "tyg_518区3排11座", "tyg_518区7排11座", "tyg_518区2排06座", "tyg_518区1排11座", "tyg_518区10排08座", "tyg_518区5排19座", "tyg_518区1排25座", "tyg_518区5排08座", "tyg_518区6排16座", "tyg_518区1排16座", "tyg_518区10排17座", "tyg_518区2排10座", "tyg_518区10排21座", "tyg_518区9排15座", "tyg_518区6排09座", "tyg_518区11排09座"], "womanSeatName": ["tyg_518区3排17座", "tyg_518区9排27座", "tyg_518区7排10座", "tyg_518区2排17座", "tyg_518区2排11座", "tyg_518区3排07座", "tyg_518区3排12座", "tyg_518区6排17座", "tyg_518区5排03座", "tyg_518区4排10座", "tyg_518区5排10座", "tyg_518区8排07座", "tyg_518区9排02座", "tyg_518区9排23座", "tyg_518区7排14座"]}, {"areaName": "519", "manNum": 36, "womanNum": 26, "maxNum": 374, "manSeatName": ["tyg_519区2排12座", "tyg_519区10排29座", "tyg_519区1排32座", "tyg_519区9排22座", "tyg_519区10排31座", "tyg_519区7排22座", "tyg_519区2排21座", "tyg_519区6排01座", "tyg_519区10排27座", "tyg_519区11排15座", "tyg_519区11排02座", "tyg_519区7排27座", "tyg_519区2排28座", "tyg_519区9排15座", "tyg_519区3排16座", "tyg_519区4排07座", "tyg_519区10排12座", "tyg_519区2排30座", "tyg_519区5排13座", "tyg_519区9排24座", "tyg_519区2排04座", "tyg_519区11排24座", "tyg_519区2排33座", "tyg_519区3排27座", "tyg_519区3排22座", "tyg_519区2排24座", "tyg_519区6排19座", "tyg_519区4排06座", "tyg_519区9排04座", "tyg_519区5排17座", "tyg_519区8排13座", "tyg_519区9排18座", "tyg_519区3排08座", "tyg_519区4排24座", "tyg_519区8排12座", "tyg_519区8排11座"], "womanSeatName": ["tyg_519区2排20座", "tyg_519区10排18座", "tyg_519区11排21座", "tyg_519区1排14座", "tyg_519区1排28座", "tyg_519区4排03座", "tyg_519区6排07座", "tyg_519区11排12座", "tyg_519区5排28座", "tyg_519区7排03座", "tyg_519区7排23座", "tyg_519区2排26座", "tyg_519区7排21座", "tyg_519区9排14座", "tyg_519区2排19座", "tyg_519区4排18座", "tyg_519区4排26座", "tyg_519区2排10座", "tyg_519区5排02座", "tyg_519区9排13座", "tyg_519区9排29座", "tyg_519区10排20座", "tyg_519区5排26座", "tyg_519区4排22座", "tyg_519区2排23座", "tyg_519区2排17座"]}, {"areaName": "520", "manNum": 46, "womanNum": 3, "maxNum": 195, "manSeatName": ["tyg_520区_1排19座", "tyg_520区_2排16座", "tyg_520区_6排14座", "tyg_520区_10排17座", "tyg_520区_9排04座", "tyg_520区_7排02座", "tyg_520区_1排11座", "tyg_520区_5排11座", "tyg_520区_8排10座", "tyg_520区_4排02座", "tyg_520区_1排06座", "tyg_520区_11排08座", "tyg_520区_1排18座", "tyg_520区_1排08座", "tyg_520区_1排15座", "tyg_520区_3排06座", "tyg_520区_5排13座", "tyg_520区_9排16座", "tyg_520区_3排03座", "tyg_520区_10排03座", "tyg_520区_3排09座", "tyg_520区_5排06座", "tyg_520区_2排10座", "tyg_520区_4排05座", "tyg_520区_11排15座", "tyg_520区_9排17座", "tyg_520区_10排05座", "tyg_520区_10排07座", "tyg_520区_11排16座", "tyg_520区_4排10座", "tyg_520区_4排14座", "tyg_520区_5排09座", "tyg_520区_8排11座", "tyg_520区_10排08座", "tyg_520区_9排12座", "tyg_520区_7排06座", "tyg_520区_2排20座", "tyg_520区_11排18座", "tyg_520区_11排07座", "tyg_520区_5排05座", "tyg_520区_10排16座", "tyg_520区_9排06座", "tyg_520区_10排10座", "tyg_520区_8排02座", "tyg_520区_2排19座", "tyg_520区_6排13座"], "womanSeatName": ["tyg_520区_10排18座", "tyg_520区_11排13座", "tyg_520区_2排04座"]}, {"areaName": "V301", "manNum": 29, "womanNum": 7, "maxNum": 36, "manSeatName": ["tyg_V301区_1排04座", "tyg_V301区_2排11座", "tyg_V301区_2排18座", "tyg_V301区_2排17座", "tyg_V301区_1排05座", "tyg_V301区_1排09座", "tyg_V301区_2排09座", "tyg_V301区_1排12座", "tyg_V301区_2排07座", "tyg_V301区_1排07座", "tyg_V301区_2排08座", "tyg_V301区_1排18座", "tyg_V301区_2排02座", "tyg_V301区_1排14座", "tyg_V301区_1排02座", "tyg_V301区_1排15座", "tyg_V301区_2排04座", "tyg_V301区_1排17座", "tyg_V301区_1排01座", "tyg_V301区_2排12座", "tyg_V301区_2排01座", "tyg_V301区_2排13座", "tyg_V301区_2排03座", "tyg_V301区_2排05座", "tyg_V301区_2排14座", "tyg_V301区_1排11座", "tyg_V301区_2排10座", "tyg_V301区_1排10座", "tyg_V301区_1排08座"], "womanSeatName": ["tyg_V301区_2排16座", "tyg_V301区_1排03座", "tyg_V301区_1排16座", "tyg_V301区_2排15座", "tyg_V301区_2排06座", "tyg_V301区_1排06座", "tyg_V301区_1排13座"]}, {"areaName": "V302", "manNum": 30, "womanNum": 6, "maxNum": 36, "manSeatName": ["tyg_V302区_1排06座", "tyg_V302区_1排02座", "tyg_V302区_2排13座", "tyg_V302区_1排11座", "tyg_V302区_2排12座", "tyg_V302区_2排03座", "tyg_V302区_2排10座", "tyg_V302区_1排14座", "tyg_V302区_2排08座", "tyg_V302区_1排18座", "tyg_V302区_2排09座", "tyg_V302区_1排15座", "tyg_V302区_2排11座", "tyg_V302区_1排01座", "tyg_V302区_2排05座", "tyg_V302区_2排01座", "tyg_V302区_1排17座", "tyg_V302区_1排04座", "tyg_V302区_2排14座", "tyg_V302区_2排15座", "tyg_V302区_1排13座", "tyg_V302区_2排07座", "tyg_V302区_2排04座", "tyg_V302区_2排02座", "tyg_V302区_1排12座", "tyg_V302区_1排05座", "tyg_V302区_2排06座", "tyg_V302区_1排09座", "tyg_V302区_1排16座", "tyg_V302区_2排18座"], "womanSeatName": ["tyg_V302区_2排16座", "tyg_V302区_1排10座", "tyg_V302区_1排08座", "tyg_V302区_2排17座", "tyg_V302区_1排07座", "tyg_V302区_1排03座"]}, {"areaName": "V303", "manNum": 4, "womanNum": 14, "maxNum": 18, "manSeatName": ["tyg_V303区_2排06座", "tyg_V303区_1排02座", "tyg_V303区_1排07座", "tyg_V303区_2排05座"], "womanSeatName": ["tyg_V303区_2排09座", "tyg_V303区_2排04座", "tyg_V303区_2排07座", "tyg_V303区_1排08座", "tyg_V303区_1排01座", "tyg_V303区_2排08座", "tyg_V303区_1排03座", "tyg_V303区_1排09座", "tyg_V303区_2排03座", "tyg_V303区_1排04座", "tyg_V303区_2排02座", "tyg_V303区_1排05座", "tyg_V303区_2排01座", "tyg_V303区_1排06座"]}, {"areaName": "V304", "manNum": 5, "womanNum": 11, "maxNum": 16, "manSeatName": ["tyg_V304区_1排05座", "tyg_V304区_1排07座", "tyg_V304区_2排05座", "tyg_V304区_2排02座", "tyg_V304区_1排06座"], "womanSeatName": ["tyg_V304区_2排06座", "tyg_V304区_2排03座", "tyg_V304区_2排08座", "tyg_V304区_1排01座", "tyg_V304区_2排01座", "tyg_V304区_1排02座", "tyg_V304区_1排04座", "tyg_V304区_2排07座", "tyg_V304区_1排08座", "tyg_V304区_2排04座", "tyg_V304区_1排03座"]}, {"areaName": "V305", "manNum": 17, "womanNum": 3, "maxNum": 20, "manSeatName": ["tyg_V305区_1排10座", "tyg_V305区_1排06座", "tyg_V305区_1排02座", "tyg_V305区_1排04座", "tyg_V305区_2排03座", "tyg_V305区_1排03座", "tyg_V305区_2排09座", "tyg_V305区_2排05座", "tyg_V305区_1排05座", "tyg_V305区_2排02座", "tyg_V305区_1排09座", "tyg_V305区_2排06座", "tyg_V305区_1排01座", "tyg_V305区_1排08座", "tyg_V305区_1排07座", "tyg_V305区_2排04座", "tyg_V305区_2排07座"], "womanSeatName": ["tyg_V305区_2排08座", "tyg_V305区_2排01座", "tyg_V305区_2排10座"]}, {"areaName": "V306", "manNum": 0, "womanNum": 10, "maxNum": 10, "manSeatName": [], "womanSeatName": ["tyg_V306区_1排01座", "tyg_V306区_2排03座", "tyg_V306区_2排05座", "tyg_V306区_2排02座", "tyg_V306区_1排02座", "tyg_V306区_2排01座", "tyg_V306区_1排05座", "tyg_V306区_1排03座", "tyg_V306区_1排04座", "tyg_V306区_2排04座"]}, {"areaName": "V307", "manNum": 4, "womanNum": 6, "maxNum": 10, "manSeatName": ["tyg_V307区_2排05座", "tyg_V307区_2排02座", "tyg_V307区_1排05座", "tyg_V307区_2排04座"], "womanSeatName": ["tyg_V307区_2排03座", "tyg_V307区_1排03座", "tyg_V307区_1排04座", "tyg_V307区_1排01座", "tyg_V307区_2排01座", "tyg_V307区_1排02座"]}, {"areaName": "V309", "manNum": 19, "womanNum": 4, "maxNum": 23, "manSeatName": ["tyg_V309区_1排02座", "tyg_V309区_2排05座", "tyg_V309区_1排11座", "tyg_V309区_1排01座", "tyg_V309区_2排11座", "tyg_V309区_1排10座", "tyg_V309区_1排04座", "tyg_V309区_2排07座", "tyg_V309区_1排09座", "tyg_V309区_1排03座", "tyg_V309区_2排12座", "tyg_V309区_2排08座", "tyg_V309区_1排08座", "tyg_V309区_2排03座", "tyg_V309区_1排06座", "tyg_V309区_2排10座", "tyg_V309区_2排09座", "tyg_V309区_2排02座", "tyg_V309区_2排04座"], "womanSeatName": ["tyg_V309区_1排07座", "tyg_V309区_2排06座", "tyg_V309区_1排05座", "tyg_V309区_2排01座"]}, {"areaName": "V310", "manNum": 0, "womanNum": 20, "maxNum": 20, "manSeatName": [], "womanSeatName": ["tyg_V310区_1排05座", "tyg_V310区_1排09座", "tyg_V310区_1排06座", "tyg_V310区_1排04座", "tyg_V310区_2排07座", "tyg_V310区_2排04座", "tyg_V310区_1排01座", "tyg_V310区_2排09座", "tyg_V310区_2排10座", "tyg_V310区_2排02座", "tyg_V310区_2排08座", "tyg_V310区_1排08座", "tyg_V310区_2排05座", "tyg_V310区_2排01座", "tyg_V310区_1排02座", "tyg_V310区_1排03座", "tyg_V310区_1排07座", "tyg_V310区_1排10座", "tyg_V310区_2排06座", "tyg_V310区_2排03座"]}, {"areaName": "V311", "manNum": 7, "womanNum": 9, "maxNum": 16, "manSeatName": ["tyg_V311区_2排02座", "tyg_V311区_2排08座", "tyg_V311区_2排05座", "tyg_V311区_2排03座", "tyg_V311区_2排06座", "tyg_V311区_1排06座", "tyg_V311区_1排07座"], "womanSeatName": ["tyg_V311区_1排02座", "tyg_V311区_1排08座", "tyg_V311区_2排07座", "tyg_V311区_1排05座", "tyg_V311区_1排01座", "tyg_V311区_1排04座", "tyg_V311区_1排03座", "tyg_V311区_2排04座", "tyg_V311区_2排01座"]}, {"areaName": "V312", "manNum": 13, "womanNum": 5, "maxNum": 18, "manSeatName": ["tyg_V312区_2排05座", "tyg_V312区_1排06座", "tyg_V312区_2排09座", "tyg_V312区_2排04座", "tyg_V312区_2排02座", "tyg_V312区_1排09座", "tyg_V312区_1排02座", "tyg_V312区_1排01座", "tyg_V312区_2排07座", "tyg_V312区_1排04座", "tyg_V312区_2排06座", "tyg_V312区_2排08座", "tyg_V312区_1排08座"], "womanSeatName": ["tyg_V312区_1排07座", "tyg_V312区_2排01座", "tyg_V312区_2排03座", "tyg_V312区_1排05座", "tyg_V312区_1排03座"]}, {"areaName": "V313", "manNum": 17, "womanNum": 19, "maxNum": 36, "manSeatName": ["tyg_V313区_1排12座", "tyg_V313区_1排15座", "tyg_V313区_2排07座", "tyg_V313区_2排16座", "tyg_V313区_2排02座", "tyg_V313区_1排17座", "tyg_V313区_2排13座", "tyg_V313区_2排10座", "tyg_V313区_1排04座", "tyg_V313区_1排18座", "tyg_V313区_1排09座", "tyg_V313区_2排15座", "tyg_V313区_1排05座", "tyg_V313区_2排03座", "tyg_V313区_1排06座", "tyg_V313区_1排16座", "tyg_V313区_1排10座"], "womanSeatName": ["tyg_V313区_2排09座", "tyg_V313区_1排11座", "tyg_V313区_2排08座", "tyg_V313区_1排03座", "tyg_V313区_1排14座", "tyg_V313区_2排18座", "tyg_V313区_2排17座", "tyg_V313区_2排04座", "tyg_V313区_2排14座", "tyg_V313区_1排02座", "tyg_V313区_2排05座", "tyg_V313区_1排13座", "tyg_V313区_2排12座", "tyg_V313区_2排01座", "tyg_V313区_1排01座", "tyg_V313区_2排11座", "tyg_V313区_2排06座", "tyg_V313区_1排07座", "tyg_V313区_1排08座"]}, {"areaName": "V314", "manNum": 0, "womanNum": 36, "maxNum": 36, "manSeatName": [], "womanSeatName": ["tyg_V314区_2排02座", "tyg_V314区_2排01座", "tyg_V314区_2排18座", "tyg_V314区_1排10座", "tyg_V314区_2排05座", "tyg_V314区_1排16座", "tyg_V314区_1排06座", "tyg_V314区_2排12座", "tyg_V314区_2排06座", "tyg_V314区_1排02座", "tyg_V314区_1排03座", "tyg_V314区_2排15座", "tyg_V314区_1排09座", "tyg_V314区_1排04座", "tyg_V314区_2排10座", "tyg_V314区_1排15座", "tyg_V314区_2排08座", "tyg_V314区_1排08座", "tyg_V314区_1排07座", "tyg_V314区_2排14座", "tyg_V314区_1排11座", "tyg_V314区_1排17座", "tyg_V314区_2排03座", "tyg_V314区_2排11座", "tyg_V314区_2排09座", "tyg_V314区_1排12座", "tyg_V314区_1排18座", "tyg_V314区_2排04座", "tyg_V314区_1排13座", "tyg_V314区_1排05座", "tyg_V314区_1排01座", "tyg_V314区_2排13座", "tyg_V314区_1排14座", "tyg_V314区_2排16座", "tyg_V314区_2排17座", "tyg_V314区_2排07座"]}, {"areaName": "V315", "manNum": 18, "womanNum": 18, "maxNum": 36, "manSeatName": ["tyg_V315区_2排06座", "tyg_V315区_1排17座", "tyg_V315区_2排09座", "tyg_V315区_1排16座", "tyg_V315区_1排05座", "tyg_V315区_2排12座", "tyg_V315区_1排01座", "tyg_V315区_1排04座", "tyg_V315区_2排03座", "tyg_V315区_1排11座", "tyg_V315区_2排07座", "tyg_V315区_1排09座", "tyg_V315区_2排11座", "tyg_V315区_2排01座", "tyg_V315区_1排08座", "tyg_V315区_2排04座", "tyg_V315区_2排16座", "tyg_V315区_2排08座"], "womanSeatName": ["tyg_V315区_2排05座", "tyg_V315区_1排03座", "tyg_V315区_2排02座", "tyg_V315区_2排17座", "tyg_V315区_2排14座", "tyg_V315区_1排12座", "tyg_V315区_2排15座", "tyg_V315区_1排13座", "tyg_V315区_2排10座", "tyg_V315区_1排10座", "tyg_V315区_1排14座", "tyg_V315区_1排15座", "tyg_V315区_1排18座", "tyg_V315区_1排07座", "tyg_V315区_2排13座", "tyg_V315区_1排06座", "tyg_V315区_1排02座", "tyg_V315区_2排18座"]}, {"areaName": "V316", "manNum": 3, "womanNum": 15, "maxNum": 18, "manSeatName": ["tyg_V316区_2排08座", "tyg_V316区_2排03座", "tyg_V316区_2排01座"], "womanSeatName": ["tyg_V316区_2排04座", "tyg_V316区_1排04座", "tyg_V316区_1排06座", "tyg_V316区_2排06座", "tyg_V316区_2排07座", "tyg_V316区_1排09座", "tyg_V316区_1排05座", "tyg_V316区_1排07座", "tyg_V316区_2排05座", "tyg_V316区_1排02座", "tyg_V316区_2排02座", "tyg_V316区_2排09座", "tyg_V316区_1排03座", "tyg_V316区_1排01座", "tyg_V316区_1排08座"]}, {"areaName": "V317", "manNum": 15, "womanNum": 28, "maxNum": 43, "manSeatName": ["tyg_V317区_1排10座", "tyg_V317区_1排07座", "tyg_V317区_2排10座", "tyg_V317区_2排02座", "tyg_V317区_2排06座", "tyg_V317区_2排04座", "tyg_V317区_1排03座", "tyg_V317区_2排20座", "tyg_V317区_1排20座", "tyg_V317区_2排13座", "tyg_V317区_2排14座", "tyg_V317区_2排09座", "tyg_V317区_1排21座", "tyg_V317区_2排08座", "tyg_V317区_2排03座"], "womanSeatName": ["tyg_V317区_1排02座", "tyg_V317区_1排13座", "tyg_V317区_1排17座", "tyg_V317区_2排07座", "tyg_V317区_1排08座", "tyg_V317区_1排18座", "tyg_V317区_1排15座", "tyg_V317区_1排06座", "tyg_V317区_2排01座", "tyg_V317区_1排05座", "tyg_V317区_2排19座", "tyg_V317区_2排11座", "tyg_V317区_1排12座", "tyg_V317区_2排15座", "tyg_V317区_1排09座", "tyg_V317区_2排18座", "tyg_V317区_1排04座", "tyg_V317区_2排12座", "tyg_V317区_1排14座", "tyg_V317区_2排16座", "tyg_V317区_2排05座", "tyg_V317区_1排11座", "tyg_V317区_1排01座", "tyg_V317区_1排19座", "tyg_V317区_2排21座", "tyg_V317区_2排17座", "tyg_V317区_1排16座", "tyg_V317区_2排22座"]}, {"areaName": "V318", "manNum": 7, "womanNum": 3, "maxNum": 10, "manSeatName": ["tyg_V318区_1排02座", "tyg_V318区_1排01座", "tyg_V318区_1排03座", "tyg_V318区_1排04座", "tyg_V318区_1排05座", "tyg_V318区_2排05座", "tyg_V318区_2排04座"], "womanSeatName": ["tyg_V318区_2排02座", "tyg_V318区_2排01座", "tyg_V318区_2排03座"]}, {"areaName": "V319", "manNum": 6, "womanNum": 4, "maxNum": 10, "manSeatName": ["tyg_V319区_1排03座", "tyg_V319区_2排02座", "tyg_V319区_2排03座", "tyg_V319区_1排01座", "tyg_V319区_1排05座", "tyg_V319区_1排04座"], "womanSeatName": ["tyg_V319区_2排04座", "tyg_V319区_2排05座", "tyg_V319区_2排01座", "tyg_V319区_1排02座"]}, {"areaName": "V320", "manNum": 3, "womanNum": 25, "maxNum": 28, "manSeatName": ["tyg_V320区_2排03座", "tyg_V320区_2排01座", "tyg_V320区_2排14座"], "womanSeatName": ["tyg_V320区_2排08座", "tyg_V320区_2排07座", "tyg_V320区_1排05座", "tyg_V320区_2排04座", "tyg_V320区_1排03座", "tyg_V320区_1排11座", "tyg_V320区_2排05座", "tyg_V320区_1排13座", "tyg_V320区_2排12座", "tyg_V320区_1排07座", "tyg_V320区_2排06座", "tyg_V320区_1排02座", "tyg_V320区_2排10座", "tyg_V320区_1排10座", "tyg_V320区_2排13座", "tyg_V320区_1排06座", "tyg_V320区_1排01座", "tyg_V320区_2排09座", "tyg_V320区_1排14座", "tyg_V320区_1排08座", "tyg_V320区_1排04座", "tyg_V320区_1排09座", "tyg_V320区_2排02座", "tyg_V320区_1排12座", "tyg_V320区_2排11座"]}, {"areaName": "V321", "manNum": 24, "womanNum": 12, "maxNum": 36, "manSeatName": ["tyg_V321区_2排05座", "tyg_V321区_2排14座", "tyg_V321区_2排07座", "tyg_V321区_2排18座", "tyg_V321区_2排06座", "tyg_V321区_1排08座", "tyg_V321区_2排09座", "tyg_V321区_1排16座", "tyg_V321区_2排04座", "tyg_V321区_1排17座", "tyg_V321区_2排02座", "tyg_V321区_1排15座", "tyg_V321区_1排01座", "tyg_V321区_1排10座", "tyg_V321区_2排13座", "tyg_V321区_1排05座", "tyg_V321区_2排17座", "tyg_V321区_1排09座", "tyg_V321区_2排11座", "tyg_V321区_2排10座", "tyg_V321区_1排03座", "tyg_V321区_1排07座", "tyg_V321区_1排14座", "tyg_V321区_1排18座"], "womanSeatName": ["tyg_V321区_1排11座", "tyg_V321区_1排04座", "tyg_V321区_2排03座", "tyg_V321区_1排02座", "tyg_V321区_1排12座", "tyg_V321区_2排15座", "tyg_V321区_2排01座", "tyg_V321区_1排06座", "tyg_V321区_2排12座", "tyg_V321区_2排08座", "tyg_V321区_1排13座", "tyg_V321区_2排16座"]}, {"areaName": "V322", "manNum": 8, "womanNum": 20, "maxNum": 28, "manSeatName": ["tyg_V322区_2排14座", "tyg_V322区_1排08座", "tyg_V322区_2排11座", "tyg_V322区_2排08座", "tyg_V322区_1排04座", "tyg_V322区_2排05座", "tyg_V322区_1排06座", "tyg_V322区_1排01座"], "womanSeatName": ["tyg_V322区_1排12座", "tyg_V322区_2排04座", "tyg_V322区_2排01座", "tyg_V322区_2排09座", "tyg_V322区_1排13座", "tyg_V322区_2排07座", "tyg_V322区_2排02座", "tyg_V322区_1排11座", "tyg_V322区_2排13座", "tyg_V322区_2排06座", "tyg_V322区_1排10座", "tyg_V322区_2排03座", "tyg_V322区_2排10座", "tyg_V322区_1排07座", "tyg_V322区_1排05座", "tyg_V322区_1排02座", "tyg_V322区_1排14座", "tyg_V322区_1排03座", "tyg_V322区_2排12座", "tyg_V322区_1排09座"]}, {"areaName": "V323", "manNum": 4, "womanNum": 6, "maxNum": 10, "manSeatName": ["tyg_V323区_2排05座", "tyg_V323区_1排02座", "tyg_V323区_1排05座", "tyg_V323区_2排03座"], "womanSeatName": ["tyg_V323区_2排02座", "tyg_V323区_1排03座", "tyg_V323区_1排01座", "tyg_V323区_2排01座", "tyg_V323区_2排04座", "tyg_V323区_1排04座"]}, {"areaName": "V324", "manNum": 5, "womanNum": 5, "maxNum": 10, "manSeatName": ["tyg_V324区_1排05座", "tyg_V324区_2排05座", "tyg_V324区_2排01座", "tyg_V324区_1排02座", "tyg_V324区_2排04座"], "womanSeatName": ["tyg_V324区_1排04座", "tyg_V324区_1排03座", "tyg_V324区_2排03座", "tyg_V324区_2排02座", "tyg_V324区_1排01座"]}, {"areaName": "V325", "manNum": 6, "womanNum": 10, "maxNum": 16, "manSeatName": ["tyg_V325区_1排06座", "tyg_V325区_1排05座", "tyg_V325区_2排07座", "tyg_V325区_1排04座", "tyg_V325区_2排04座", "tyg_V325区_1排07座"], "womanSeatName": ["tyg_V325区_1排03座", "tyg_V325区_1排02座", "tyg_V325区_1排01座", "tyg_V325区_2排06座", "tyg_V325区_2排03座", "tyg_V325区_2排02座", "tyg_V325区_2排01座", "tyg_V325区_1排08座", "tyg_V325区_2排08座", "tyg_V325区_2排05座"]}, {"areaName": "V326", "manNum": 10, "womanNum": 8, "maxNum": 18, "manSeatName": ["tyg_V326区_2排06座", "tyg_V326区_1排03座", "tyg_V326区_2排04座", "tyg_V326区_1排04座", "tyg_V326区_2排07座", "tyg_V326区_1排02座", "tyg_V326区_1排07座", "tyg_V326区_1排06座", "tyg_V326区_2排08座", "tyg_V326区_2排01座"], "womanSeatName": ["tyg_V326区_1排09座", "tyg_V326区_1排08座", "tyg_V326区_2排09座", "tyg_V326区_1排01座", "tyg_V326区_2排05座", "tyg_V326区_2排02座", "tyg_V326区_1排05座", "tyg_V326区_2排03座"]}, {"areaName": "V327", "manNum": 21, "womanNum": 15, "maxNum": 36, "manSeatName": ["tyg_V327区_1排14座", "tyg_V327区_1排04座", "tyg_V327区_1排11座", "tyg_V327区_1排03座", "tyg_V327区_2排17座", "tyg_V327区_1排10座", "tyg_V327区_2排02座", "tyg_V327区_2排15座", "tyg_V327区_1排02座", "tyg_V327区_2排05座", "tyg_V327区_2排04座", "tyg_V327区_1排13座", "tyg_V327区_1排05座", "tyg_V327区_1排07座", "tyg_V327区_2排07座", "tyg_V327区_2排01座", "tyg_V327区_2排06座", "tyg_V327区_1排15座", "tyg_V327区_2排10座", "tyg_V327区_1排16座", "tyg_V327区_2排13座"], "womanSeatName": ["tyg_V327区_2排16座", "tyg_V327区_1排09座", "tyg_V327区_1排17座", "tyg_V327区_1排08座", "tyg_V327区_1排12座", "tyg_V327区_1排01座", "tyg_V327区_2排12座", "tyg_V327区_1排06座", "tyg_V327区_2排14座", "tyg_V327区_2排03座", "tyg_V327区_1排18座", "tyg_V327区_2排18座", "tyg_V327区_2排09座", "tyg_V327区_2排11座", "tyg_V327区_2排08座"]}, {"areaName": "V401", "manNum": 1, "womanNum": 35, "maxNum": 36, "manSeatName": ["tyg_V401区_2排15座"], "womanSeatName": ["tyg_V401区_1排01座", "tyg_V401区_1排04座", "tyg_V401区_2排02座", "tyg_V401区_1排13座", "tyg_V401区_2排12座", "tyg_V401区_1排16座", "tyg_V401区_1排03座", "tyg_V401区_2排05座", "tyg_V401区_2排08座", "tyg_V401区_1排02座", "tyg_V401区_1排05座", "tyg_V401区_2排04座", "tyg_V401区_2排07座", "tyg_V401区_1排18座", "tyg_V401区_2排03座", "tyg_V401区_1排14座", "tyg_V401区_2排13座", "tyg_V401区_2排14座", "tyg_V401区_2排10座", "tyg_V401区_1排12座", "tyg_V401区_2排18座", "tyg_V401区_2排11座", "tyg_V401区_1排09座", "tyg_V401区_2排01座", "tyg_V401区_1排08座", "tyg_V401区_1排11座", "tyg_V401区_2排16座", "tyg_V401区_2排06座", "tyg_V401区_2排17座", "tyg_V401区_1排17座", "tyg_V401区_1排15座", "tyg_V401区_2排09座", "tyg_V401区_1排07座", "tyg_V401区_1排10座", "tyg_V401区_1排06座"]}, {"areaName": "V402", "manNum": 22, "womanNum": 14, "maxNum": 36, "manSeatName": ["tyg_V402区_2排12座", "tyg_V402区_2排03座", "tyg_V402区_2排18座", "tyg_V402区_1排05座", "tyg_V402区_1排18座", "tyg_V402区_1排11座", "tyg_V402区_1排01座", "tyg_V402区_1排04座", "tyg_V402区_2排08座", "tyg_V402区_1排17座", "tyg_V402区_2排05座", "tyg_V402区_2排17座", "tyg_V402区_2排04座", "tyg_V402区_2排16座", "tyg_V402区_1排13座", "tyg_V402区_2排15座", "tyg_V402区_1排06座", "tyg_V402区_2排06座", "tyg_V402区_1排14座", "tyg_V402区_2排07座", "tyg_V402区_1排09座", "tyg_V402区_1排12座"], "womanSeatName": ["tyg_V402区_1排03座", "tyg_V402区_2排01座", "tyg_V402区_1排07座", "tyg_V402区_2排10座", "tyg_V402区_2排02座", "tyg_V402区_1排15座", "tyg_V402区_1排08座", "tyg_V402区_1排16座", "tyg_V402区_2排13座", "tyg_V402区_2排14座", "tyg_V402区_1排10座", "tyg_V402区_1排02座", "tyg_V402区_2排11座", "tyg_V402区_2排09座"]}, {"areaName": "V403", "manNum": 8, "womanNum": 10, "maxNum": 18, "manSeatName": ["tyg_V403区_2排02座", "tyg_V403区_1排03座", "tyg_V403区_1排04座", "tyg_V403区_2排09座", "tyg_V403区_1排01座", "tyg_V403区_2排01座", "tyg_V403区_2排04座", "tyg_V403区_2排06座"], "womanSeatName": ["tyg_V403区_1排09座", "tyg_V403区_2排07座", "tyg_V403区_1排02座", "tyg_V403区_1排08座", "tyg_V403区_2排03座", "tyg_V403区_2排08座", "tyg_V403区_1排07座", "tyg_V403区_1排06座", "tyg_V403区_1排05座", "tyg_V403区_2排05座"]}, {"areaName": "V404", "manNum": 14, "womanNum": 2, "maxNum": 16, "manSeatName": ["tyg_V404区_1排07座", "tyg_V404区_1排01座", "tyg_V404区_1排04座", "tyg_V404区_1排02座", "tyg_V404区_2排03座", "tyg_V404区_2排07座", "tyg_V404区_2排02座", "tyg_V404区_2排04座", "tyg_V404区_2排05座", "tyg_V404区_2排06座", "tyg_V404区_2排08座", "tyg_V404区_2排01座", "tyg_V404区_1排06座", "tyg_V404区_1排03座"], "womanSeatName": ["tyg_V404区_1排08座", "tyg_V404区_1排05座"]}, {"areaName": "V405", "manNum": 7, "womanNum": 13, "maxNum": 20, "manSeatName": ["tyg_V405区_1排03座", "tyg_V405区_2排10座", "tyg_V405区_1排10座", "tyg_V405区_2排07座", "tyg_V405区_2排04座", "tyg_V405区_2排06座", "tyg_V405区_2排05座"], "womanSeatName": ["tyg_V405区_2排01座", "tyg_V405区_1排07座", "tyg_V405区_1排08座", "tyg_V405区_2排03座", "tyg_V405区_2排08座", "tyg_V405区_1排04座", "tyg_V405区_1排02座", "tyg_V405区_1排09座", "tyg_V405区_2排09座", "tyg_V405区_1排05座", "tyg_V405区_1排06座", "tyg_V405区_1排01座", "tyg_V405区_2排02座"]}, {"areaName": "V406", "manNum": 3, "womanNum": 7, "maxNum": 10, "manSeatName": ["tyg_V406区_1排02座", "tyg_V406区_2排02座", "tyg_V406区_1排04座"], "womanSeatName": ["tyg_V406区_2排03座", "tyg_V406区_2排05座", "tyg_V406区_1排03座", "tyg_V406区_1排01座", "tyg_V406区_1排05座", "tyg_V406区_2排01座", "tyg_V406区_2排04座"]}, {"areaName": "V407", "manNum": 8, "womanNum": 2, "maxNum": 10, "manSeatName": ["tyg_V407区_1排03座", "tyg_V407区_2排04座", "tyg_V407区_1排02座", "tyg_V407区_2排05座", "tyg_V407区_2排01座", "tyg_V407区_1排04座", "tyg_V407区_2排03座", "tyg_V407区_1排01座"], "womanSeatName": ["tyg_V407区_2排02座", "tyg_V407区_1排05座"]}, {"areaName": "V408", "manNum": 7, "womanNum": 38, "maxNum": 100, "manSeatName": ["tyg_V408区_1排18座", "tyg_V408区_2排50座", "tyg_V408区_2排24座", "tyg_V408区_2排15座", "tyg_V408区_1排37座", "tyg_V408区_2排38座", "tyg_V408区_1排29座"], "womanSeatName": ["tyg_V408区_1排16座", "tyg_V408区_2排31座", "tyg_V408区_2排30座", "tyg_V408区_2排16座", "tyg_V408区_2排27座", "tyg_V408区_1排27座", "tyg_V408区_2排17座", "tyg_V408区_2排28座", "tyg_V408区_2排18座", "tyg_V408区_1排49座", "tyg_V408区_2排37座", "tyg_V408区_1排26座", "tyg_V408区_1排06座", "tyg_V408区_1排42座", "tyg_V408区_2排07座", "tyg_V408区_1排46座", "tyg_V408区_1排43座", "tyg_V408区_2排12座", "tyg_V408区_2排48座", "tyg_V408区_2排44座", "tyg_V408区_1排09座", "tyg_V408区_1排12座", "tyg_V408区_2排49座", "tyg_V408区_1排03座", "tyg_V408区_1排19座", "tyg_V408区_1排04座", "tyg_V408区_2排45座", "tyg_V408区_1排50座", "tyg_V408区_1排39座", "tyg_V408区_1排22座", "tyg_V408区_1排21座", "tyg_V408区_2排40座", "tyg_V408区_2排47座", "tyg_V408区_1排31座", "tyg_V408区_1排32座", "tyg_V408区_1排38座", "tyg_V408区_1排01座", "tyg_V408区_2排39座"]}, {"areaName": "V409", "manNum": 15, "womanNum": 8, "maxNum": 23, "manSeatName": ["tyg_V409区_2排07座", "tyg_V409区_1排11座", "tyg_V409区_2排11座", "tyg_V409区_1排02座", "tyg_V409区_2排03座", "tyg_V409区_2排12座", "tyg_V409区_2排10座", "tyg_V409区_1排09座", "tyg_V409区_2排01座", "tyg_V409区_1排03座", "tyg_V409区_2排02座", "tyg_V409区_2排05座", "tyg_V409区_1排05座", "tyg_V409区_2排06座", "tyg_V409区_1排06座"], "womanSeatName": ["tyg_V409区_2排08座", "tyg_V409区_2排09座", "tyg_V409区_1排04座", "tyg_V409区_1排07座", "tyg_V409区_1排08座", "tyg_V409区_1排01座", "tyg_V409区_2排04座", "tyg_V409区_1排10座"]}, {"areaName": "V410", "manNum": 4, "womanNum": 16, "maxNum": 20, "manSeatName": ["tyg_V410区_1排01座", "tyg_V410区_1排04座", "tyg_V410区_1排05座", "tyg_V410区_1排06座"], "womanSeatName": ["tyg_V410区_1排03座", "tyg_V410区_2排01座", "tyg_V410区_1排07座", "tyg_V410区_1排02座", "tyg_V410区_2排08座", "tyg_V410区_2排02座", "tyg_V410区_2排07座", "tyg_V410区_2排09座", "tyg_V410区_1排08座", "tyg_V410区_1排09座", "tyg_V410区_1排10座", "tyg_V410区_2排06座", "tyg_V410区_2排10座", "tyg_V410区_2排03座", "tyg_V410区_2排04座", "tyg_V410区_2排05座"]}, {"areaName": "V411", "manNum": 7, "womanNum": 9, "maxNum": 16, "manSeatName": ["tyg_V411区_2排04座", "tyg_V411区_1排08座", "tyg_V411区_2排06座", "tyg_V411区_2排03座", "tyg_V411区_1排07座", "tyg_V411区_1排06座", "tyg_V411区_2排08座"], "womanSeatName": ["tyg_V411区_1排02座", "tyg_V411区_2排02座", "tyg_V411区_2排05座", "tyg_V411区_1排03座", "tyg_V411区_2排01座", "tyg_V411区_1排01座", "tyg_V411区_2排07座", "tyg_V411区_1排05座", "tyg_V411区_1排04座"]}, {"areaName": "V412", "manNum": 6, "womanNum": 12, "maxNum": 18, "manSeatName": ["tyg_V412区_1排09座", "tyg_V412区_1排02座", "tyg_V412区_1排07座", "tyg_V412区_2排02座", "tyg_V412区_1排01座", "tyg_V412区_2排09座"], "womanSeatName": ["tyg_V412区_2排04座", "tyg_V412区_2排03座", "tyg_V412区_2排05座", "tyg_V412区_1排06座", "tyg_V412区_2排08座", "tyg_V412区_1排04座", "tyg_V412区_1排08座", "tyg_V412区_1排05座", "tyg_V412区_1排03座", "tyg_V412区_2排06座", "tyg_V412区_2排01座", "tyg_V412区_2排07座"]}, {"areaName": "V413", "manNum": 1, "womanNum": 35, "maxNum": 36, "manSeatName": ["tyg_V413区_2排09座"], "womanSeatName": ["tyg_V413区_1排12座", "tyg_V413区_1排07座", "tyg_V413区_2排17座", "tyg_V413区_1排01座", "tyg_V413区_2排07座", "tyg_V413区_1排03座", "tyg_V413区_2排14座", "tyg_V413区_2排10座", "tyg_V413区_1排04座", "tyg_V413区_1排14座", "tyg_V413区_2排13座", "tyg_V413区_1排10座", "tyg_V413区_2排01座", "tyg_V413区_1排08座", "tyg_V413区_2排12座", "tyg_V413区_2排11座", "tyg_V413区_2排03座", "tyg_V413区_2排08座", "tyg_V413区_1排17座", "tyg_V413区_2排15座", "tyg_V413区_1排06座", "tyg_V413区_2排02座", "tyg_V413区_2排16座", "tyg_V413区_1排16座", "tyg_V413区_2排18座", "tyg_V413区_1排09座", "tyg_V413区_2排05座", "tyg_V413区_2排06座", "tyg_V413区_1排18座", "tyg_V413区_1排11座", "tyg_V413区_1排13座", "tyg_V413区_1排15座", "tyg_V413区_2排04座", "tyg_V413区_1排05座", "tyg_V413区_1排02座"]}, {"areaName": "V414", "manNum": 24, "womanNum": 12, "maxNum": 36, "manSeatName": ["tyg_V414区_1排16座", "tyg_V414区_2排17座", "tyg_V414区_1排13座", "tyg_V414区_2排09座", "tyg_V414区_1排05座", "tyg_V414区_2排15座", "tyg_V414区_1排08座", "tyg_V414区_2排10座", "tyg_V414区_2排06座", "tyg_V414区_2排14座", "tyg_V414区_2排11座", "tyg_V414区_2排04座", "tyg_V414区_1排14座", "tyg_V414区_1排09座", "tyg_V414区_2排16座", "tyg_V414区_2排03座", "tyg_V414区_2排08座", "tyg_V414区_2排13座", "tyg_V414区_1排17座", "tyg_V414区_1排07座", "tyg_V414区_2排05座", "tyg_V414区_1排03座", "tyg_V414区_1排02座", "tyg_V414区_2排18座"], "womanSeatName": ["tyg_V414区_1排15座", "tyg_V414区_1排12座", "tyg_V414区_2排01座", "tyg_V414区_1排06座", "tyg_V414区_1排11座", "tyg_V414区_2排02座", "tyg_V414区_2排07座", "tyg_V414区_1排01座", "tyg_V414区_1排04座", "tyg_V414区_2排12座", "tyg_V414区_1排10座", "tyg_V414区_1排18座"]}, {"areaName": "V415", "manNum": 23, "womanNum": 13, "maxNum": 36, "manSeatName": ["tyg_V415区_2排16座", "tyg_V415区_2排07座", "tyg_V415区_1排11座", "tyg_V415区_2排01座", "tyg_V415区_2排06座", "tyg_V415区_2排11座", "tyg_V415区_1排06座", "tyg_V415区_2排05座", "tyg_V415区_1排18座", "tyg_V415区_1排05座", "tyg_V415区_1排10座", "tyg_V415区_1排16座", "tyg_V415区_2排10座", "tyg_V415区_2排04座", "tyg_V415区_1排04座", "tyg_V415区_2排17座", "tyg_V415区_2排13座", "tyg_V415区_1排02座", "tyg_V415区_2排14座", "tyg_V415区_1排13座", "tyg_V415区_1排15座", "tyg_V415区_1排08座", "tyg_V415区_2排09座"], "womanSeatName": ["tyg_V415区_1排14座", "tyg_V415区_2排03座", "tyg_V415区_1排03座", "tyg_V415区_2排18座", "tyg_V415区_2排08座", "tyg_V415区_1排12座", "tyg_V415区_1排01座", "tyg_V415区_2排12座", "tyg_V415区_1排09座", "tyg_V415区_1排17座", "tyg_V415区_2排15座", "tyg_V415区_1排07座", "tyg_V415区_2排02座"]}, {"areaName": "V416", "manNum": 7, "womanNum": 11, "maxNum": 18, "manSeatName": ["tyg_V416区_1排06座", "tyg_V416区_2排08座", "tyg_V416区_1排01座", "tyg_V416区_2排06座", "tyg_V416区_2排09座", "tyg_V416区_2排04座", "tyg_V416区_2排05座"], "womanSeatName": ["tyg_V416区_1排05座", "tyg_V416区_1排04座", "tyg_V416区_1排09座", "tyg_V416区_2排03座", "tyg_V416区_1排03座", "tyg_V416区_2排07座", "tyg_V416区_2排02座", "tyg_V416区_1排08座", "tyg_V416区_1排02座", "tyg_V416区_1排07座", "tyg_V416区_2排01座"]}, {"areaName": "V417", "manNum": 28, "womanNum": 15, "maxNum": 43, "manSeatName": ["tyg_V417区_1排11座", "tyg_V417区_1排07座", "tyg_V417区_1排05座", "tyg_V417区_1排17座", "tyg_V417区_2排01座", "tyg_V417区_1排04座", "tyg_V417区_1排09座", "tyg_V417区_2排19座", "tyg_V417区_1排06座", "tyg_V417区_2排07座", "tyg_V417区_1排03座", "tyg_V417区_2排12座", "tyg_V417区_2排18座", "tyg_V417区_1排20座", "tyg_V417区_1排02座", "tyg_V417区_2排21座", "tyg_V417区_1排15座", "tyg_V417区_1排12座", "tyg_V417区_2排05座", "tyg_V417区_2排04座", "tyg_V417区_2排10座", "tyg_V417区_2排06座", "tyg_V417区_2排08座", "tyg_V417区_1排18座", "tyg_V417区_1排21座", "tyg_V417区_2排20座", "tyg_V417区_1排08座", "tyg_V417区_1排10座"], "womanSeatName": ["tyg_V417区_1排14座", "tyg_V417区_1排13座", "tyg_V417区_1排16座", "tyg_V417区_1排01座", "tyg_V417区_2排14座", "tyg_V417区_2排15座", "tyg_V417区_2排16座", "tyg_V417区_2排22座", "tyg_V417区_2排02座", "tyg_V417区_2排03座", "tyg_V417区_2排11座", "tyg_V417区_2排13座", "tyg_V417区_2排17座", "tyg_V417区_2排09座", "tyg_V417区_1排19座"]}, {"areaName": "V418", "manNum": 3, "womanNum": 7, "maxNum": 10, "manSeatName": ["tyg_V418区_1排04座", "tyg_V418区_1排03座", "tyg_V418区_2排04座"], "womanSeatName": ["tyg_V418区_1排01座", "tyg_V418区_2排01座", "tyg_V418区_2排03座", "tyg_V418区_1排05座", "tyg_V418区_1排02座", "tyg_V418区_2排05座", "tyg_V418区_2排02座"]}, {"areaName": "V419", "manNum": 9, "womanNum": 1, "maxNum": 10, "manSeatName": ["tyg_V419区_2排05座", "tyg_V419区_1排02座", "tyg_V419区_1排01座", "tyg_V419区_1排05座", "tyg_V419区_2排02座", "tyg_V419区_1排03座", "tyg_V419区_2排03座", "tyg_V419区_2排01座", "tyg_V419区_2排04座"], "womanSeatName": ["tyg_V419区_1排04座"]}, {"areaName": "V420", "manNum": 12, "womanNum": 16, "maxNum": 28, "manSeatName": ["tyg_V420区_1排07座", "tyg_V420区_1排03座", "tyg_V420区_1排12座", "tyg_V420区_2排12座", "tyg_V420区_2排10座", "tyg_V420区_1排11座", "tyg_V420区_2排11座", "tyg_V420区_2排05座", "tyg_V420区_2排06座", "tyg_V420区_1排02座", "tyg_V420区_1排08座", "tyg_V420区_1排05座"], "womanSeatName": ["tyg_V420区_2排07座", "tyg_V420区_1排10座", "tyg_V420区_2排09座", "tyg_V420区_1排01座", "tyg_V420区_2排03座", "tyg_V420区_2排04座", "tyg_V420区_1排14座", "tyg_V420区_2排02座", "tyg_V420区_2排08座", "tyg_V420区_2排13座", "tyg_V420区_1排04座", "tyg_V420区_2排14座", "tyg_V420区_1排06座", "tyg_V420区_1排09座", "tyg_V420区_1排13座", "tyg_V420区_2排01座"]}, {"areaName": "V421", "manNum": 9, "womanNum": 27, "maxNum": 36, "manSeatName": ["tyg_V421区_2排17座", "tyg_V421区_1排11座", "tyg_V421区_2排07座", "tyg_V421区_2排08座", "tyg_V421区_2排18座", "tyg_V421区_2排02座", "tyg_V421区_2排03座", "tyg_V421区_2排14座", "tyg_V421区_2排10座"], "womanSeatName": ["tyg_V421区_2排16座", "tyg_V421区_2排06座", "tyg_V421区_1排13座", "tyg_V421区_2排04座", "tyg_V421区_1排01座", "tyg_V421区_1排18座", "tyg_V421区_1排07座", "tyg_V421区_1排08座", "tyg_V421区_2排15座", "tyg_V421区_2排09座", "tyg_V421区_1排02座", "tyg_V421区_1排09座", "tyg_V421区_1排14座", "tyg_V421区_2排01座", "tyg_V421区_1排17座", "tyg_V421区_1排05座", "tyg_V421区_2排12座", "tyg_V421区_2排05座", "tyg_V421区_2排11座", "tyg_V421区_2排13座", "tyg_V421区_1排10座", "tyg_V421区_1排03座", "tyg_V421区_1排06座", "tyg_V421区_1排16座", "tyg_V421区_1排12座", "tyg_V421区_1排04座", "tyg_V421区_1排15座"]}, {"areaName": "V422", "manNum": 25, "womanNum": 3, "maxNum": 28, "manSeatName": ["tyg_V422区_1排10座", "tyg_V422区_1排13座", "tyg_V422区_2排06座", "tyg_V422区_2排12座", "tyg_V422区_2排01座", "tyg_V422区_2排07座", "tyg_V422区_1排11座", "tyg_V422区_1排12座", "tyg_V422区_1排05座", "tyg_V422区_1排06座", "tyg_V422区_2排04座", "tyg_V422区_2排08座", "tyg_V422区_2排14座", "tyg_V422区_1排14座", "tyg_V422区_2排02座", "tyg_V422区_2排11座", "tyg_V422区_2排03座", "tyg_V422区_1排01座", "tyg_V422区_1排03座", "tyg_V422区_1排07座", "tyg_V422区_2排10座", "tyg_V422区_1排02座", "tyg_V422区_2排05座", "tyg_V422区_2排09座", "tyg_V422区_1排09座"], "womanSeatName": ["tyg_V422区_2排13座", "tyg_V422区_1排04座", "tyg_V422区_1排08座"]}, {"areaName": "V423", "manNum": 9, "womanNum": 1, "maxNum": 10, "manSeatName": ["tyg_V423区_2排05座", "tyg_V423区_2排01座", "tyg_V423区_1排01座", "tyg_V423区_1排05座", "tyg_V423区_2排02座", "tyg_V423区_1排04座", "tyg_V423区_2排04座", "tyg_V423区_1排03座", "tyg_V423区_1排02座"], "womanSeatName": ["tyg_V423区_2排03座"]}, {"areaName": "V424", "manNum": 0, "womanNum": 10, "maxNum": 10, "manSeatName": [], "womanSeatName": ["tyg_V424区_1排05座", "tyg_V424区_2排02座", "tyg_V424区_1排04座", "tyg_V424区_1排02座", "tyg_V424区_1排01座", "tyg_V424区_2排05座", "tyg_V424区_1排03座", "tyg_V424区_2排01座", "tyg_V424区_2排04座", "tyg_V424区_2排03座"]}, {"areaName": "V425", "manNum": 7, "womanNum": 9, "maxNum": 16, "manSeatName": ["tyg_V425区_2排07座", "tyg_V425区_2排01座", "tyg_V425区_1排05座", "tyg_V425区_2排06座", "tyg_V425区_1排02座", "tyg_V425区_1排07座", "tyg_V425区_2排02座"], "womanSeatName": ["tyg_V425区_2排03座", "tyg_V425区_2排04座", "tyg_V425区_2排08座", "tyg_V425区_1排06座", "tyg_V425区_1排04座", "tyg_V425区_1排08座", "tyg_V425区_2排05座", "tyg_V425区_1排03座", "tyg_V425区_1排01座"]}, {"areaName": "V426", "manNum": 9, "womanNum": 9, "maxNum": 18, "manSeatName": ["tyg_V426区_2排06座", "tyg_V426区_1排03座", "tyg_V426区_2排04座", "tyg_V426区_1排07座", "tyg_V426区_1排04座", "tyg_V426区_2排01座", "tyg_V426区_2排09座", "tyg_V426区_2排03座", "tyg_V426区_1排02座"], "womanSeatName": ["tyg_V426区_1排08座", "tyg_V426区_1排05座", "tyg_V426区_1排09座", "tyg_V426区_2排08座", "tyg_V426区_2排02座", "tyg_V426区_1排06座", "tyg_V426区_2排07座", "tyg_V426区_2排05座", "tyg_V426区_1排01座"]}, {"areaName": "V427", "manNum": 28, "womanNum": 8, "maxNum": 36, "manSeatName": ["tyg_V427区_2排12座", "tyg_V427区_2排03座", "tyg_V427区_1排07座", "tyg_V427区_1排10座", "tyg_V427区_2排09座", "tyg_V427区_1排11座", "tyg_V427区_1排12座", "tyg_V427区_2排10座", "tyg_V427区_1排09座", "tyg_V427区_2排07座", "tyg_V427区_2排08座", "tyg_V427区_2排17座", "tyg_V427区_2排14座", "tyg_V427区_1排08座", "tyg_V427区_2排18座", "tyg_V427区_1排04座", "tyg_V427区_1排18座", "tyg_V427区_1排06座", "tyg_V427区_2排04座", "tyg_V427区_1排03座", "tyg_V427区_2排05座", "tyg_V427区_2排11座", "tyg_V427区_1排15座", "tyg_V427区_1排05座", "tyg_V427区_1排17座", "tyg_V427区_2排06座", "tyg_V427区_1排16座", "tyg_V427区_2排15座"], "womanSeatName": ["tyg_V427区_2排13座", "tyg_V427区_1排14座", "tyg_V427区_2排01座", "tyg_V427区_2排02座", "tyg_V427区_1排13座", "tyg_V427区_2排16座", "tyg_V427区_1排01座", "tyg_V427区_1排02座"]}, {"areaName": "主席台区", "manNum": 29, "womanNum": 12, "maxNum": 41, "manSeatName": ["tyg_主席台区_3排08座", "tyg_主席台区_4排03座", "tyg_主席台区_1排06座", "tyg_主席台区_2排07座", "tyg_主席台区_3排01座", "tyg_主席台区_4排07座", "tyg_主席台区_2排04座", "tyg_主席台区_2排02座", "tyg_主席台区_1排02座", "tyg_主席台区_4排05座", "tyg_主席台区_3排04座", "tyg_主席台区_3排03座", "tyg_主席台区_4排01座", "tyg_主席台区_4排02座", "tyg_主席台区_2排05座", "tyg_主席台区_2排09座", "tyg_主席台区_2排10座", "tyg_主席台区_1排09座", "tyg_主席台区_2排01座", "tyg_主席台区_4排09座", "tyg_主席台区_3排11座", "tyg_主席台区_1排08座", "tyg_主席台区_4排10座", "tyg_主席台区_1排01座", "tyg_主席台区_4排04座", "tyg_主席台区_3排10座", "tyg_主席台区_3排07座", "tyg_主席台区_3排09座", "tyg_主席台区_3排06座"], "womanSeatName": ["tyg_主席台区_1排05座", "tyg_主席台区_2排08座", "tyg_主席台区_2排03座", "tyg_主席台区_3排05座", "tyg_主席台区_3排02座", "tyg_主席台区_4排06座", "tyg_主席台区_1排07座", "tyg_主席台区_1排04座", "tyg_主席台区_2排06座", "tyg_主席台区_4排08座", "tyg_主席台区_2排11座", "tyg_主席台区_1排03座"]}, {"areaName": "贵宾1区", "manNum": 32, "womanNum": 6, "maxNum": 132, "manSeatName": ["tyg_贵宾1区_6排07座", "tyg_贵宾1区_6排12座", "tyg_贵宾1区_1排08座", "tyg_贵宾1区_1排04座", "tyg_贵宾1区_0排13座", "tyg_贵宾1区_6排11座", "tyg_贵宾1区_8排16座", "tyg_贵宾1区_7排02座", "tyg_贵宾1区_6排16座", "tyg_贵宾1区_3排13座", "tyg_贵宾1区_0排02座", "tyg_贵宾1区_5排03座", "tyg_贵宾1区_4排12座", "tyg_贵宾1区_5排11座", "tyg_贵宾1区_7排18座", "tyg_贵宾1区_8排15座", "tyg_贵宾1区_7排16座", "tyg_贵宾1区_5排10座", "tyg_贵宾1区_0排06座", "tyg_贵宾1区_2排07座", "tyg_贵宾1区_8排05座", "tyg_贵宾1区_8排02座", "tyg_贵宾1区_8排09座", "tyg_贵宾1区_7排07座", "tyg_贵宾1区_2排13座", "tyg_贵宾1区_1排02座", "tyg_贵宾1区_0排09座", "tyg_贵宾1区_7排10座", "tyg_贵宾1区_4排03座", "tyg_贵宾1区_8排06座", "tyg_贵宾1区_2排10座", "tyg_贵宾1区_3排03座"], "womanSeatName": ["tyg_贵宾1区_3排11座", "tyg_贵宾1区_7排06座", "tyg_贵宾1区_4排04座", "tyg_贵宾1区_6排10座", "tyg_贵宾1区_6排13座", "tyg_贵宾1区_4排13座"]}, {"areaName": "贵宾2区", "manNum": 6, "womanNum": 44, "maxNum": 132, "manSeatName": ["tyg_主席台区_8排16座", "tyg_贵宾2区_2排06座", "tyg_主席台区_6排17座", "tyg_主席台区_6排09座", "tyg_贵宾2区_1排08座", "tyg_贵宾2区_4排05座"], "womanSeatName": ["tyg_贵宾2区_4排08座", "tyg_主席台区_8排18座", "tyg_贵宾2区_5排01座", "tyg_贵宾2区_5排05座", "tyg_贵宾2区_4排13座", "tyg_主席台区_8排07座", "tyg_贵宾2区_2排07座", "tyg_贵宾2区_0排13座", "tyg_贵宾2区_1排04座", "tyg_主席台区_6排03座", "tyg_主席台区_8排05座", "tyg_贵宾2区_2排10座", "tyg_贵宾2区_2排05座", "tyg_主席台区_7排02座", "tyg_主席台区_7排18座", "tyg_贵宾2区_2排13座", "tyg_主席台区_6排15座", "tyg_主席台区_7排05座", "tyg_主席台区_8排03座", "tyg_贵宾2区_1排05座", "tyg_贵宾2区_1排12座", "tyg_主席台区_7排01座", "tyg_贵宾2区_3排13座", "tyg_主席台区_7排11座", "tyg_主席台区_7排12座", "tyg_贵宾2区_4排09座", "tyg_贵宾2区_0排07座", "tyg_贵宾2区_1排03座", "tyg_贵宾2区_5排08座", "tyg_贵宾2区_0排06座", "tyg_主席台区_7排10座", "tyg_贵宾2区_2排11座", "tyg_主席台区_8排14座", "tyg_贵宾2区_2排08座", "tyg_贵宾2区_1排13座", "tyg_贵宾2区_5排02座", "tyg_主席台区_6排05座", "tyg_主席台区_6排07座", "tyg_贵宾2区_3排08座", "tyg_主席台区_8排02座", "tyg_主席台区_8排12座", "tyg_主席台区_6排04座", "tyg_贵宾2区_3排03座", "tyg_贵宾2区_5排12座"]}]}, "clpz": {"start": true, "startpos_x": 166.637817, "startpos_y": -1.43000793, "startpos_z": 79.24452, "end": true, "endpos_x": 143.094818, "endpos_y": -1.43000674, "endpos_z": 76.7355347}, "zawpz": {"barrierInfos": [{"barrierName": "施工牌", "pos_x": -2.922014, "pos_y": 0.09995478, "pos_z": -28.1043777, "angle_x": 0.0, "angle_y": 1.00179122e-05, "angle_z": 0.0, "scale_x": 1.1591, "scale_y": 1.1591, "scale_z": 1.1591}, {"barrierName": "栏杆", "pos_x": -6.644413, "pos_y": 0.09995103, "pos_z": -17.7837486, "angle_x": 0.0, "angle_y": 1.00179122e-05, "angle_z": 0.0, "scale_x": 1.0, "scale_y": 4.96370268, "scale_z": 1.0}, {"barrierName": "栏杆", "pos_x": -21.4806786, "pos_y": 0.5721938, "pos_z": -27.421751, "angle_x": 0.0, "angle_y": 1.00179122e-05, "angle_z": 0.0, "scale_x": 1.0, "scale_y": 1.0, "scale_z": 1.0}, {"barrierName": "施工牌", "pos_x": -10.1621656, "pos_y": 0.09995622, "pos_z": -32.15999, "angle_x": 0.0, "angle_y": 1.00179122e-05, "angle_z": 0.0, "scale_x": 1.1591, "scale_y": 1.1591, "scale_z": 1.1591}], "fireInfos": [{"pos_x": 6.30764341, "pos_y": 0.09994995, "pos_z": -14.6113653, "angle_x": 0.0, "angle_y": 0.0, "angle_z": 0.0, "scale_x": 1.0, "scale_y": 1.0, "scale_z": 1.0}, {"pos_x": -20.8140774, "pos_y": 0.264237523, "pos_z": -14.4434156, "angle_x": 0.0, "angle_y": 0.0, "angle_z": 0.0, "scale_x": 1.0, "scale_y": 1.0, "scale_z": 1.0}], "carInfo": {"start": true, "startpos_x": 166.637817, "startpos_y": -1.43000793, "startpos_z": 79.24452, "end": true, "endpos_x": 143.094818, "endpos_y": -1.43000674, "endpos_z": 76.7355347}}, "crkpz": {"datas": [{"name": "G1", "state": true, "isCanConfig": true}, {"name": "G2", "state": true, "isCanConfig": true}, {"name": "G3", "state": true, "isCanConfig": true}, {"name": "G4", "state": true, "isCanConfig": true}, {"name": "G5", "state": true, "isCanConfig": true}, {"name": "G6", "state": true, "isCanConfig": true}, {"name": "G7", "state": true, "isCanConfig": true}, {"name": "G8", "state": true, "isCanConfig": true}, {"name": "G9", "state": true, "isCanConfig": true}, {"name": "G10", "state": true, "isCanConfig": true}, {"name": "A1", "state": true, "isCanConfig": true}, {"name": "A2", "state": true, "isCanConfig": true}, {"name": "A3", "state": true, "isCanConfig": true}, {"name": "A4", "state": true, "isCanConfig": true}, {"name": "B1", "state": true, "isCanConfig": true}, {"name": "B2", "state": true, "isCanConfig": true}, {"name": "B3", "state": true, "isCanConfig": true}, {"name": "C1", "state": true, "isCanConfig": true}, {"name": "C2", "state": true, "isCanConfig": true}, {"name": "C3", "state": true, "isCanConfig": true}, {"name": "C4", "state": true, "isCanConfig": true}, {"name": "D1", "state": true, "isCanConfig": true}, {"name": "D2", "state": true, "isCanConfig": true}, {"name": "D3", "state": true, "isCanConfig": true}, {"name": "E_G1", "state": true, "isCanConfig": true}, {"name": "E_G2", "state": true, "isCanConfig": true}, {"name": "E_G3", "state": true, "isCanConfig": true}, {"name": "E_G4", "state": true, "isCanConfig": true}, {"name": "E_G7", "state": true, "isCanConfig": true}, {"name": "E_G8", "state": true, "isCanConfig": true}, {"name": "E_G10", "state": true, "isCanConfig": true}]}, "ssys": "26.51448", "ssrs": 39, "zlrs": 0, "gckljssrs": {"evacuateDatas": [{"exitName": "B2", "personNum": 19}, {"exitName": "D2", "personNum": 10}, {"exitName": "B1", "personNum": 2}, {"exitName": "B3", "personNum": 5}, {"exitName": "D1", "personNum": 2}, {"exitName": "D3", "personNum": 1}]}, "startTime": "2025-02-28T14:31:40.374691+08:00"}