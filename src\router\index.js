/*
 * @Author: hkh <EMAIL>
 * @Date: 2024-07-16 16:13:16
 * @LastEditors: hkh <EMAIL>
 * @LastEditTime: 2025-02-27 16:28:38
 * @FilePath: \chengde-computer-room\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHashHistory } from "vue-router";
import { getToken } from "@/apis";

const routes = [
    {
        path: "",
        redirect: "/yljl",
    },
    {
        path: "",
        children: [
            {
                path: "/yljl",
                name: "yljl",
                component: () => import("@/views/yljl"),
            },
            {
                path: "/yljlxq",
                name: "yljlxq",
                component: () => import("@/views/yljl/detail"),
            },
            {
                path: "/yjya",
                name: "yjya",
                component: () => import("@/views/yjya"),
            },

            {
                path: "/yjsj",
                name: "yjsj",
                component: () => import("@/views/yjsj"),
            },
            {
                path: "/yjzzjg",
                name: "yjzzjg",
                component: () => import("@/views/yjzzjg"),
            },
            {
                path: "/yjjydw",
                name: "yjjydw",
                component: () => import("@/views/yjjydw"),
            },
            {
                path: "/wzck",
                name: "wzck",
                component: () => import("@/views/wzck"),
            },
            {
                path: "/dxzb",
                name: "dxzb",
                component: () => import("@/views/dxzb"),
            },
            {
                path: "/pbb",
                name: "pbb",
                component: () => import("@/views/pbb"),
            },
        ],
    },
];

export const router = createRouter({
    history: createWebHashHistory(),
    routes: routes,
});
