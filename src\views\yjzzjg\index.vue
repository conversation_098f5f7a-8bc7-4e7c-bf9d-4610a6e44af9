<template>
    <div class="right">
        <BaseTitle />
        <div class="body">
            <div class="title">应急组织架构</div>
            <div class="search-box">
                <div class="tab-box">
                    <div
                        class="tab"
                        @click="changeTab(tab.code)"
                        :class="{ activeTab: activeTab === tab.code }"
                        v-for="tab in systemDictionary"
                        :key="tab.code"
                    >
                        {{ tab.name }}
                    </div>
                </div>
                <div class="add-btn">
                    <el-button type="primary" @click="handleAdd"> 新增 </el-button>
                </div>
            </div>
            <div class="table-box">
                <el-table
                    ref="tableRef"
                    :data="tableData"
                    row-key="id"
                    style="width: 100%"
                    :height="tableHeight"
                    :loading="tableLoading"
                    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                    :header-cell-style="{
                        background: '#f5f7fa',
                        color: '#303133',
                        fontWeight: '600',
                        fontSize: '14px',
                        height: '50px',
                    }"
                    :row-style="{ height: '48px' }"
                    :cell-style="{ fontSize: '13px', color: '#606266' }"
                    :default-expand-all="true"
                >
                    <el-table-column prop="nodeName" label="组织名称" width="200" />
                    <el-table-column prop="personName" label="负责人姓名" width="200" />
                    <el-table-column prop="phone" label="联系方式" width="200" />
                    <el-table-column prop="systemName" label="所属系统" width="180" />
                    <el-table-column prop="updateTime" label="更新日期" width="180" />
                    <el-table-column label="操作">
                        <template #default="scope">
                        
                            <el-button type="text" @click="handleAddChild(scope.row)"
                                >添加下级</el-button
                            >
                            <el-button type="text" @click="handleView(scope.row)">详情</el-button>
                            <el-popconfirm title="确认删除?" @confirm="handleDelete(scope.row)">
                                <template #reference>
                                    <el-button type="text" style="color: #f56c6c">删除</el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <!-- 新增/编辑弹框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogTitle"
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form :model="formData" label-width="120px" :rules="rules" ref="formRef">
                <el-form-item label="所属系统" prop="systemCode" required>
                    <el-select
                        v-model="formData.systemCode"
                        placeholder="请选择所属系统"
                        style="width: 100%"
                        @change="handleSystemChange"
                    >
                        <el-option
                            v-for="system in systemDictionary"
                            :key="system.code"
                            :label="system.name"
                            :value="system.code"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="组织名称" prop="name">
                    <el-input v-model="formData.name" placeholder="请输入组织名称" />
                </el-form-item>
                <el-form-item label="负责人姓名" prop="personName">
                    <el-input v-model="formData.personName" placeholder="请输入负责人姓名" />
                </el-form-item>
                <el-form-item label="联系方式" prop="contact">
                    <el-input v-model="formData.contact" placeholder="请输入联系方式" />
                </el-form-item>
                <el-form-item label="上级组织" v-if="dialogMode === 'addChild'">
                    <el-input v-model="parentOrgName" disabled />
                </el-form-item>
                <el-form-item label="下级组织" v-if="dialogMode === 'addParent'">
                    <el-input v-model="childOrgName" disabled />
                </el-form-item>
                <el-form-item label="职责描述">
                    <el-input
                        v-model="formData.description"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入职责描述"
                    />
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">确定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 详情弹框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="组织详情"
            width="600px"
            :close-on-click-modal="false"
        >
            <el-form :model="detailData" label-width="120px">
                <el-form-item label="组织名称">
                    <span class="form-value">{{ detailData.nodeName || "-" }}</span>
                </el-form-item>
                <el-form-item label="负责人姓名">
                    <span class="form-value">{{ detailData.personName || "-" }}</span>
                </el-form-item>
                <el-form-item label="联系方式">
                    <span class="form-value">{{ detailData.phone || "-" }}</span>
                </el-form-item>
                <el-form-item label="所属系统">
                    <span class="form-value">{{
                        getSystemName(detailData.eventCode) || "-"
                    }}</span>
                </el-form-item>
                <el-form-item label="职责描述">
                    <span class="form-value">{{ detailData.remark || "-" }}</span>
                </el-form-item>
                <el-form-item label="更新时间">
                    <span class="form-value">{{ detailData.updateTime || "-" }}</span>
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, inject } from "vue";
import { useRouter } from "vue-router";
import BaseTitle from "@/components/BaseTitle/index.vue";
import { ElMessage } from "element-plus";
import { IocStructSave,IocStructGetTree,IocStructDelete } from "@/apis";
const router = useRouter();

// 获取全局字典数据
const dictData = inject("dictData");

// 所属系统字典数据 - 从全局字典获取
const systemDictionary = computed(() => {
    return dictData.value.map((item) => ({
        name: item.dictValue,
        code: item.dictCode,
    }));
    
});

// 默认选择第一个系统，如果没有数据则使用默认值
const activeTab = ref("belong_system_fire_control");

// 当字典数据加载完成后，设置默认选中的系统
onMounted(() => {
    if (systemDictionary.value.length > 0) {
        activeTab.value = systemDictionary.value[0].code;
    }
    console.log(systemDictionary.value);
});

// 表格数据
const tableRef = ref();
const tableData = ref([]);
const tableLoading = ref(false);

// 计算表格高度
const tableHeight = computed(() => {
    // 计算可用高度：总高度 - 标题 - 搜索框 - 内边距
    return "calc(100vh - 320px)";
});

// 弹框相关
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const dialogMode = ref("add"); // add, edit, addChild, addParent
const formRef = ref();

const formData = reactive({
    id: null,
    name: "",
    personName: "",
    contact: "",
    systemCode: "",
    systemName: "",
    description: "",
    parentId: null,
});

const detailData = ref({});
const parentOrgName = ref("");
const childOrgName = ref("");

// 表单验证规则
const rules = {
    name: [{ required: true, message: "请输入组织名称", trigger: "blur" }],
    personName: [{ required: true, message: "请输入负责人姓名", trigger: "blur" }],
    contact: [
        { required: true, message: "请输入联系方式", trigger: "blur" },
        { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" },
    ],
    systemCode: [{ required: true, message: "请选择所属系统", trigger: "change" }],
};

// 计算属性
const dialogTitle = computed(() => {
    switch (dialogMode.value) {
        case "add":
            return "新增组织";
        case "edit":
            return "编辑组织";
        case "addChild":
            return "添加下级组织";
        case "addParent":
            return "添加上级组织";
        default:
            return "组织管理";
    }
});

// 初始化数据 - 移除模拟数据，使用空数组
const initData = () => {
    tableData.value = [];
};

// 工具函数
const getSystemName = (systemCode) => {
    const system = systemDictionary.value.find((item) => item.code === systemCode);
    return system ? system.name : "";
};

const handleSystemChange = () => {
    const selectedSystem = systemDictionary.value.find((item) => item.code === formData.systemCode);
    if (selectedSystem) {
        formData.systemName = selectedSystem.name;
    }
    console.log(formData,378);
};

// 切换标签页
const changeTab = (tab) => {
    activeTab.value = tab;
    console.log('当前选中的eventCode:', tab);
    loadData();
};

// 加载数据
const loadData = async () => {
    tableLoading.value = true;
    try {
        // 调用IocStructGetTree接口获取树形数据
        const response = await IocStructGetTree({ eventCode: activeTab.value });
        console.log('IocStructGetTree接口返回结果:', response);
        
        // 处理接口返回的数据
        if (response && response.data) {
            // 处理接口返回的数据，添加systemName字段
            const processedData = response.data.map(item => ({
                ...item,
                systemName: getSystemName(item.eventCode) || ''
            }));
            tableData.value = processedData;
        } else {
            // 如果没有数据，设置为空数组
            tableData.value = [];
        }
    } catch (error) {
        console.error('获取数据失败:', error);
        // 接口失败时设置为空数组
        tableData.value = [];
    } finally {
        tableLoading.value = false;
    }
};

// 重置表单
const resetForm = () => {
    Object.assign(formData, {
        id: null,
        name: "",
        personName: "",
        contact: "",
        systemCode: activeTab.value,
        systemName: getSystemName(activeTab.value),
        description: "",
        parentId: null,
    });
    parentOrgName.value = "";
    childOrgName.value = "";
};

// 新增
const handleAdd = () => {
    dialogMode.value = "add";
    resetForm();
    dialogVisible.value = true;
};

// // 添加上级
// const handleAddParent = (row) => {
//     dialogMode.value = "addParent";
//     resetForm();
//     childOrgName.value = row.name;
//     formData.childId = row.id;
//     formData.systemCode = row.systemCode;
//     formData.systemName = row.systemName;
//     dialogVisible.value = true;
// };

// 添加下级
const handleAddChild = (row) => {
    dialogMode.value = "addChild";
    resetForm();
    parentOrgName.value = row.nodeName; // 使用nodeName字段
    formData.parentId = row.id; // 设置父节点ID
    formData.systemCode = row.eventCode; // 使用eventCode字段
    formData.systemName = getSystemName(row.eventCode); // 根据eventCode获取系统名称
    dialogVisible.value = true;
};

// 查看详情
const handleView = (row) => {
    detailData.value = { ...row };
    detailDialogVisible.value = true;
};

// 删除
const handleDelete = async (row) => {
    try {
        // 准备删除接口数据，传入ids参数
        const apiData = {
            ids: row.id // 传入要删除的记录ID
        };
        
        console.log('调用IocStructDelete删除接口的数据:', apiData);
        
        // 调用删除接口
        const response = await IocStructDelete(apiData);
        console.log('删除接口返回结果:', response);
        
        ElMessage.success("删除成功");
        loadData();
    } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error("删除失败，请重试");
    }
};

// 提交表单
const handleSubmit = () => {
    formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                // 准备接口数据
                const apiData = {
                    eventCode: formData.systemCode, // 从所属系统获取event_code
                    nodeName: formData.name, // 节点名称（组织名称）
                    personName: formData.personName, // 负责人姓名
                    phone: formData.contact, // 手机号
                    remark: formData.description, // 备注（职责描述）
                    parentId: formData.parentId // 父节点ID，用于添加下级节点
                };
                
                console.log('调用IocStructSave接口的数据:', apiData);
                
                // 调用保存接口
                const response = await IocStructSave(apiData);
                console.log('接口返回结果:', response);
                
                ElMessage.success("操作成功");
                dialogVisible.value = false;
                loadData();
            } catch (error) {
                console.error('保存失败:', error);
                ElMessage.error("保存失败，请重试");
            }
        }
    });
};

onMounted(() => {
    loadData();
});
</script>

<style scoped lang="scss">
.right {
    width: calc(100% - 336px);
}

.body {
    padding: 50px 50px;
    height: calc(100% - 122px);
    display: flex;
    flex-direction: column;

    .title {
        margin-bottom: 50px;
        font-size: 20px;
        font-weight: bold;
    }
}

.search-box {
    min-height: 120px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 20px 62px;
    background: #ffffff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;
    position: relative;

    .tab-box {
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
        gap: 12px 16px;
        max-width: calc(100% - 120px);
        line-height: 1.4;

        .tab {
            min-width: 90px;
            height: 36px;
            line-height: 34px;
            text-align: center;
            cursor: pointer;
            padding: 0 12px;
            border: 1px solid #e4e7ed;
            border-radius: 18px;
            color: #606266;
            transition: all 0.3s ease;
            font-size: 13px;
            white-space: nowrap;
            background: #ffffff;
            box-sizing: border-box;

            &.activeTab {
                color: #ffffff;
                background: linear-gradient(135deg, #2993fd 0%, #1e7ce8 100%);
                border-color: #2993fd;
                box-shadow: 0 2px 8px rgba(41, 147, 253, 0.3);
                transform: translateY(-1px);
            }

            &:hover:not(.activeTab) {
                color: #2993fd;
                border-color: #2993fd;
                background: #f0f8ff;
                transform: translateY(-1px);
                box-shadow: 0 2px 6px rgba(41, 147, 253, 0.15);
            }
        }
    }

    .add-btn {
        margin-left: 20px;
        flex-shrink: 0;
        align-self: flex-start;
        margin-top: 2px;
    }
}

.table-box {
    margin-top: 40px;
    flex: 1;
    padding: 30px 62px;
    background: #fff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;
    display: flex;
    flex-direction: column;
}

.form-value {
    color: #303133;
    font-size: 14px;
    line-height: 32px;
}

.dialog-footer {
    text-align: right;
    border-top: 1px solid #ebeef5;
    padding-top: 15px;
    margin-top: 20px;
}

:deep(.el-table) {
    border: 1px solid #ebeef5;
    border-radius: 4px;
}

:deep(.el-table .el-table__header-wrapper) {
    border-radius: 4px 4px 0 0;
}

:deep(.el-table td, .el-table th) {
    border-bottom: 1px solid #ebeef5;
}

:deep(.el-table th) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table td) {
    border-right: 1px solid #ebeef5;
}

:deep(.el-table th:last-child) {
    border-right: none;
}

:deep(.el-table td:last-child) {
    border-right: none;
}

:deep(.el-table__body-wrapper) {
    flex: 1;
}

.table-box :deep(.el-table) {
    height: 100% !important;
}

:deep(.el-dialog) {
    border-radius: 8px;
}

:deep(.el-dialog__header) {
    background: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    padding: 20px 24px;
}

:deep(.el-dialog__title) {
    color: #303133;
    font-weight: 600;
    font-size: 16px;
}

:deep(.el-dialog__body) {
    padding: 24px;
}

:deep(.el-form-item) {
    margin-bottom: 20px;
}

:deep(.el-form-item__label) {
    color: #606266;
    font-weight: 500;
}

:deep(.el-button) {
    border-radius: 4px;
    padding: 8px 16px;
}

:deep(.el-button--primary) {
    background: #409eff;
    border-color: #409eff;

    &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
    }
}
</style>
