import { isFunction, UUID, isArray } from "@/public/utils/common";

export const useState = (initialState, evaluationCallback, { intervalTime = 0 } = {}) => {
    if (isRef(initialState)) {
        console.error("please not set to ref");
    }
    const state = ref(initialState);
    const setState = (value) => {
        if (isFunction(value)) {
            Promise.resolve()
                .then(() => {
                    return value(state.value);
                })
                .then((v) => {
                    state.value = v;
                });
        } else {
            state.value = value;
        }
    };
    if (evaluationCallback) {
        setState(evaluationCallback);
    }
    if (intervalTime) {
        let timer = setInterval(() => {
            setState(evaluationCallback);
        }, intervalTime);
        onScopeDispose(() => {
            if (timer) {
                clearInterval(timer);
                timer = null;
            }
        });
    }
    return [state, setState];
};

export const useEffect = (func, rely = [], { immediate = true } = {}) => {
    watch(
        [...(isArray(rely) ? rely : [rely]), () => null],
        (v, ov, onCleanup) => {
            nextTick(() => {
                const cb = func(v, ov);
                if (isFunction(cb)) {
                    onCleanup(cb);
                }
            });
        },
        {
            immediate,
        },
    );
};

export const useMemo = (func, rely) => {
    const memoRef = ref(func()); //menu1
    useEffect(
        (v, ov) => {
            memoRef.value = func(v, ov);
        },
        rely,
        { immediate: false },
    );

    return memoRef;
};
