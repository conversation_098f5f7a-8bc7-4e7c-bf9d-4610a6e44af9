<template>
    <div class="title">
        <div>标题</div>
        <div class="user">
            <div class="name">{{ user.name }}</div>
            <div class="img-box">
                <img src="@/assets/vector.png" alt="" />
            </div>
            <el-icon><ArrowDownBold /></el-icon>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const user = reactive({
    name: "韩大力",
});
</script>

<style scoped lang="scss">
.title {
    height: 122px;
    padding: 0 50px;
    background: #f7f8fc;
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.16);
    border-radius: 0px 0px 0px 0px;
    z-index: -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .user {
        display: flex;
        align-items: center;
        .name {
            margin-right: 10px;
        }
        .img-box {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
        }
        .el-icon {
            font-size: 20px;
            color: #000;
            cursor: pointer;
        }
    }
}
</style>
