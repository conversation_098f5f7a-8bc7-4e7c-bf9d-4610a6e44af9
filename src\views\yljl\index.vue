<template>
    <div class="right">
        <BaseTitle />
        <div class="body">
            <div class="title">演练记录管理</div>
            <div class="search-box">
                <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                    <el-form-item label="演练名称">
                        <el-input v-model="searchForm.drillName" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="事件类型">
                        <el-select
                            v-model="searchForm.drillEventType"
                            placeholder="请选择"
                            clearable
                        >
                            <el-option
                                v-for="type in eventTypeDictionary"
                                :key="type.value"
                                :label="type.name"
                                :value="type.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="演练状态">
                        <el-select v-model="searchForm.drillStatus" placeholder="请选择" clearable>
                            <el-option label="待演练" :value="0" />
                            <el-option label="已完成" :value="1" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">查询</el-button>
                        <el-button @click="onReset">重置</el-button>
                    </el-form-item>
                </el-form>
                <div class="add-btn">
                    <el-button type="primary" @click="handleAddDrill">新增演练</el-button>
                </div>
            </div>
            <div class="table-box">
                <el-table
                    ref="multipleTableRef"
                    :data="tableData"
                    row-key="id"
                    style="width: 100%; height: 451px"
                    :loading="tableLoading"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column property="id" label="ID" width="100" />
                    <el-table-column property="drillName" label="演练名称" min-width="200" />
                    <el-table-column property="drillEventType" label="事件类型" width="120">
                        <template #default="scope">
                            {{ getEventTypeName(scope.row.drillEventType) }}
                        </template>
                    </el-table-column>
                    <el-table-column property="plannedDrillTime" label="计划时间" width="180">
                        <template #default="scope">
                            {{ formatTime(scope.row.plannedDrillTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column property="plannedDrillLocation" label="计划地点" width="150" />
                    <el-table-column property="drillStatus" label="演练状态" width="120">
                        <template #default="scope">
                            <el-tag :type="scope.row.drillStatus === 1 ? 'success' : 'warning'">
                                {{ scope.row.drillStatus === 1 ? "已完成" : "待演练" }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template #default="scope">
                            <el-button
                                v-if="scope.row.drillStatus === 0"
                                type="text"
                                @click="handleComplete(scope.row)"
                            >
                                完成演练
                            </el-button>
                            <el-button type="text" @click="handleEdit(scope.row)">详情</el-button>
                            <el-popconfirm title="确认删除?" @confirm="handleDelete(scope.row)">
                                <template #reference>
                                    <el-button type="text">删除</el-button>
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50]"
                    :disabled="disabled"
                    :background="background"
                    layout=" prev, pager, next, total, sizes, jumper"
                    :total="total"
                    class="page-box"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>
    </div>

    <!-- 新增演练弹窗 -->
    <el-dialog v-model="drillDialogVisible" title="新增演练" width="600px" class="drill-dialog">
        <div class="dialog-content">
            <el-form :model="drillForm" ref="drillFormRef" label-width="120px" class="drill-form">
                <el-form-item label="演练名称" prop="drillName" required>
                    <el-input
                        v-model="drillForm.drillName"
                        placeholder="请输入演练名称"
                        size="large"
                    />
                </el-form-item>
                <el-form-item label="事件类型" prop="drillEventType" required>
                    <el-select
                        v-model="drillForm.drillEventType"
                        placeholder="请选择事件类型"
                        style="width: 100%"
                        size="large"
                    >
                        <el-option
                            v-for="type in eventTypeDictionary"
                            :key="type.value"
                            :label="type.name"
                            :value="type.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="计划演练时间" prop="plannedDrillTime" required>
                    <el-date-picker
                        v-model="drillForm.plannedDrillTime"
                        type="datetime"
                        placeholder="请选择计划演练时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                        size="large"
                    />
                </el-form-item>
                <el-form-item label="计划演练地点" prop="plannedDrillLocation" required>
                    <el-input
                        v-model="drillForm.plannedDrillLocation"
                        placeholder="请输入计划演练地点"
                        size="large"
                    />
                </el-form-item>
                <el-form-item label="演练目的" prop="drillPurpose">
                    <el-input
                        v-model="drillForm.drillPurpose"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入演练目的..."
                        resize="none"
                    />
                </el-form-item>
                <el-form-item label="应急预案ID" prop="emergencyPlanId">
                    <el-input
                        v-model="drillForm.emergencyPlanId"
                        placeholder="请输入应急预案ID（可选）"
                        size="large"
                    />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input
                        v-model="drillForm.remark"
                        type="textarea"
                        :rows="3"
                        placeholder="请输入备注..."
                        resize="none"
                    />
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button size="large" @click="drillDialogVisible = false">取消</el-button>
                <el-button type="primary" size="large" @click="handleSaveDrill">确认</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 完成演练弹窗 -->
    <el-dialog
        v-model="completeDialogVisible"
        title="完成演练"
        width="600px"
        class="complete-dialog"
    >
        <div class="dialog-content">
            <el-form
                :model="completeForm"
                ref="completeFormRef"
                label-width="120px"
                class="complete-form"
            >
                <el-form-item label="实际演练时间" prop="actualDrillTime" required>
                    <el-date-picker
                        v-model="completeForm.actualDrillTime"
                        type="datetime"
                        placeholder="请选择实际演练时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                        size="large"
                    />
                </el-form-item>
                <el-form-item label="实际演练地点" prop="actualDrillLocation" required>
                    <el-input
                        v-model="completeForm.actualDrillLocation"
                        placeholder="请输入实际演练地点"
                        size="large"
                    />
                </el-form-item>
                <el-form-item label="演练总结文档" prop="drillSummaryDoc">
                    <div class="document-input-container">
                        <el-input
                            v-model="completeForm.drillSummaryDoc"
                            placeholder="请输入总结文档路径或描述"
                            size="large"
                        />
                        <el-button
                            v-if="completeForm.drillSummaryDoc"
                            type="primary"
                            size="large"
                            @click="downloadCompleteDocument"
                            class="download-btn"
                        >
                            下载
                        </el-button>
                    </div>
                </el-form-item>
                <el-form-item label="总结文档上传">
                    <el-upload
                        class="upload-demo"
                        drag
                        action=""
                        :before-upload="uploadFile"
                        :show-file-list="false"
                    >
                        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                        <div class="el-upload__text">上传总结文档</div>
                    </el-upload>
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button size="large" @click="completeDialogVisible = false">取消</el-button>
                <el-button type="primary" size="large" @click="handleSaveComplete"
                    >完成演练</el-button
                >
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, watch, reactive, onMounted, inject, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import BaseTitle from "@/components/BaseTitle/index.vue";
import {
    YjylList,
    YjylDel,
    DrillList,
    DrillAdd,
    DrillComplete,
    Drilldelete,
    DrillDownload,
    bupload,
} from "@/apis";
import { ElMessage } from "element-plus";
import { InfoFilled, UploadFilled } from "@element-plus/icons-vue";
import dayjs from "dayjs";

const router = useRouter();
const route = useRoute();

const multipleTableRef = ref("");
const multipleSelection = ref([]);
const drillFormRef = ref(null);
const completeFormRef = ref(null);

// 获取全局字典数据
const dictData = inject("dictData");

// 系统字典数据（如果需要的话）
const systemDictionary = computed(() => {
    return dictData.value
        .filter((item) => item.dictKey === "event_type")
        .map((item) => ({
            name: item.dictValue,
            code: item.dictCode,
        }));
});

// 事件类型字典数据（显示所有字典项）
const eventTypeDictionary = computed(() => {
    return dictData.value.map((item) => ({
        name: item.dictValue,
        value: item.dictCode || item.id,
    }));
});

const tableData = ref([]);
const tableLoading = ref(false);

// 弹窗状态
const drillDialogVisible = ref(false);
const completeDialogVisible = ref(false);

// 新增演练表单数据
const drillForm = reactive({
    drillName: "",
    drillEventType: null,
    plannedDrillTime: "",
    plannedDrillLocation: "",
    drillPurpose: "",
    emergencyPlanId: "",
    remark: "",
    drillStatus: 0, // 默认待演练
});

// 完成演练表单数据
const completeForm = reactive({
    id: null,
    actualDrillTime: "",
    actualDrillLocation: "",
    drillSummaryDoc: "",
});

// 当前选中的演练记录
const currentDrill = ref(null);

// 格式化时间函数
const formatTime = (timeString) => {
    if (!timeString) return "";
    return dayjs(timeString).format("YYYY-MM-DD HH:mm");
};

// 获取事件类型名称
const getEventTypeName = (typeValue) => {
    const eventType = eventTypeDictionary.value.find((type) => type.value === typeValue);
    return eventType ? eventType.name : "其他";
};

const handleSelectionChange = () => {
    multipleSelection.value = val;
};

const onSubmit = () => {
    currentPage.value = 1;
    getDrillList(1, pageSize.value);
};

const onReset = () => {
    currentPage.value = 1;
    pageSize.value = 10;
    searchForm.drillName = "";
    searchForm.drillEventType = null;
    searchForm.drillStatus = null;
    getDrillList(1, 10);
};

// 获取演练列表
const getDrillList = async (page = 1, size = 10) => {
    tableLoading.value = true;
    try {
        const params = {
            pageNum: page,
            pageSize: size,
        };

        // 添加搜索条件
        if (searchForm.drillName) {
            params.drillName = searchForm.drillName;
        }
        if (searchForm.drillEventType !== null) {
            params.drillEventType = searchForm.drillEventType;
        }
        if (searchForm.drillStatus !== null) {
            params.drillStatus = searchForm.drillStatus;
        }

        const res = await DrillList(params);

        if (res && res.code === 200) {
            tableData.value = res.data || [];
            total.value = res.total || 0;
        } else {
            ElMessage.error(res.msg || "获取数据失败");
        }
    } catch (error) {
        console.error("获取演练列表失败：", error);
        ElMessage.error("获取数据失败");
    } finally {
        tableLoading.value = false;
    }
};

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const background = ref(false);
const disabled = ref(false);

const handleSizeChange = (val) => {
    pageSize.value = val;
    getDrillList(currentPage.value, val);
};
const handleCurrentChange = (val) => {
    currentPage.value = val;
    getDrillList(val, pageSize.value);
};

const searchForm = reactive({
    drillName: "",
    drillEventType: null,
    drillStatus: null,
});

onMounted(() => {
    getDrillList();
});

const handleComplete = (row) => {
    // 完成演练
    currentDrill.value = row;
    completeForm.id = row.id;
    completeForm.actualDrillTime = "";
    completeForm.actualDrillLocation = "";
    completeForm.drillSummaryDoc = "";
    completeDialogVisible.value = true;
};

const handleEdit = (row) => {
    // 详情
    router.push({
        path: "/yljlxq",
        query: {
            id: row.id,
        },
    });
};

const handleDelete = async (row) => {
    try {
        const res = await Drilldelete({
            ids: row.id,
        });
        if (res && res.code === 200) {
            ElMessage.success("删除成功");
            getDrillList(currentPage.value, pageSize.value);
        } else {
            ElMessage.error(res.msg || "删除失败");
        }
    } catch (error) {
        console.error("删除失败：", error);
        ElMessage.error("删除失败");
    }
};

// 新增演练
const handleAddDrill = () => {
    drillForm.drillName = "";
    drillForm.drillEventType = null;
    drillForm.plannedDrillTime = "";
    drillForm.plannedDrillLocation = "";
    drillForm.drillPurpose = "";
    drillForm.emergencyPlanId = "";
    drillForm.remark = "";
    drillForm.drillStatus = 0;
    drillDialogVisible.value = true;
};

// 保存演练
const handleSaveDrill = async () => {
    if (
        !drillForm.drillName ||
        !drillForm.drillEventType ||
        !drillForm.plannedDrillTime ||
        !drillForm.plannedDrillLocation
    ) {
        ElMessage.warning("请填写完整信息");
        return;
    }

    try {
        const res = await DrillAdd(drillForm);
        if (res && res.code === 200) {
            ElMessage.success("演练创建成功");
            drillDialogVisible.value = false;
            getDrillList(currentPage.value, pageSize.value);
        } else {
            ElMessage.error(res.msg || "创建失败");
        }
    } catch (error) {
        console.error("创建演练失败：", error);
        ElMessage.error("创建失败");
    }
};

// 保存完成演练
const handleSaveComplete = async () => {
    if (!completeForm.actualDrillTime || !completeForm.actualDrillLocation) {
        ElMessage.warning("请填写完整信息");
        return;
    }

    try {
        // 合并当前演练信息和完成信息
        const completeData = {
            ...currentDrill.value,
            ...completeForm,
            drillStatus: 1, // 设置为已完成
        };

        const res = await DrillComplete(completeData);
        if (res && res.code === 200) {
            ElMessage.success("演练完成成功");
            completeDialogVisible.value = false;
            getDrillList(currentPage.value, pageSize.value);
        } else {
            ElMessage.error(res.msg || "完成失败");
        }
    } catch (error) {
        console.error("完成演练失败：", error);
        ElMessage.error("完成失败");
    }
};

// 文件上传
const uploadFile = (file) => {
    // 创建一个 FormData 对象，用于存储要上传的文件
    const formData = new FormData();
    formData.append("file", file);
    console.log(formData, file);

    // fetch('/api/upload', {
    //   method: 'POST',
    //   body: formData,
    //   // 注意：不要设置Content-Type，浏览器会自动设置正确的boundary
    // });

    // 发起上传请求
    bupload(formData)
        .then((res) => {
            ElMessage({
                message: "文件上传成功",
                type: "success",
            });
            completeForm.drillSummaryDoc = res.data;
            // debugger;
            // onSubmit();
            return true;
        })
        .catch((error) => {
            console.error("文件上传出错", error);
            ElMessage({
                message: "文件上传出错",
                type: "error",
            });
            return false;
        });
    return false;
};

// 下载完成演练弹窗中的文档
const downloadCompleteDocument = async () => {
    if (!completeForm.drillSummaryDoc) {
        ElMessage.warning("没有可下载的文档");
        return;
    }

    try {
        const fileName = completeForm.drillSummaryDoc;
        const response = await DrillDownload({ fileName });

        // // 创建下载链接
        // const blob = new Blob([response], { type: "application/octet-stream" });
        // const url = window.URL.createObjectURL(blob);
        // const link = document.createElement("a");
        // link.href = url;

        // // 从文件名中提取实际文件名（去掉路径）
        // const actualFileName = fileName.split("/").pop() || fileName;
        // link.download = actualFileName;

        // document.body.appendChild(link);
        // link.click();
        // document.body.removeChild(link);
        // window.URL.revokeObjectURL(url);

        ElMessage.success("文档下载成功");
    } catch (error) {
        console.error("下载文档失败：", error);
        ElMessage.error("下载文档失败");
    }
};
</script>

<style scoped lang="scss">
.right {
    width: calc(100% - 336px);
}
.body {
    padding: 50px 50px;
    height: calc(100% - 122px);
    .title {
        margin-bottom: 50px;
        font-size: 20px;
        font-weight: bold;
    }
}
.search-box {
    height: 124px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 62px;
    background: #ffffff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;

    .add-btn {
        margin-left: auto;
    }
}
.table-box {
    margin-top: 40px;
    height: calc(100% - 214px);
    padding: 30px 62px;
    background: #fff;
    box-shadow: 0px 1px 24px 0px rgba(0, 0, 0, 0.04);
    border-radius: 20px 20px 20px 20px;
    .page-box {
        display: flex;
        justify-content: flex-end;
        margin-top: 30px;
    }
}
.el-form-item {
    margin-bottom: 0;
}
.demo-form-inline .el-input {
    --el-input-width: 220px;
}

.demo-form-inline .el-select {
    --el-select-width: 220px;
}

/* 弹窗样式优化 */
.drill-dialog,
.complete-dialog {
    .el-dialog {
        border-radius: 12px;
    }

    .el-dialog__header {
        padding: 24px 24px 16px;
        border-bottom: 1px solid #ebeef5;

        .el-dialog__title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
    }

    .el-dialog__body {
        padding: 0;
    }

    .el-dialog__footer {
        padding: 16px 24px 24px;
        border-top: 1px solid #ebeef5;
    }
}

.dialog-content {
    padding: 24px;
}

.drill-form,
.complete-form {
    .el-form-item {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .el-form-item__label {
        font-weight: 500;
        color: #606266;
        line-height: 40px;

        &::before {
            color: #f56c6c;
            margin-right: 4px;
        }
    }

    .el-input,
    .el-select,
    .el-textarea {
        .el-input__wrapper {
            border-radius: 8px;
            transition: all 0.3s;

            &:hover {
                box-shadow: 0 0 0 1px #409eff inset;
            }
        }
    }

    .el-textarea .el-textarea__inner {
        border-radius: 8px;
        padding: 12px 16px;
        line-height: 1.5;
        font-size: 14px;
    }

    .document-input-container {
        display: flex;
        align-items: center;
        gap: 12px;

        .el-input {
            flex: 1;
        }

        .download-btn {
            flex-shrink: 0;
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;

    .el-button {
        padding: 12px 32px;
        border-radius: 8px;
        font-weight: 500;
        min-width: 100px;

        &.el-button--primary {
            background: linear-gradient(135deg, #409eff 0%, #367cc7 100%);
            border-color: #409eff;

            &:hover {
                background: linear-gradient(135deg, #66b1ff 0%, #5a8dd1 100%);
            }
        }

        &:not(.el-button--primary) {
            border-color: #dcdfe6;
            color: #606266;

            &:hover {
                border-color: #409eff;
                color: #409eff;
            }
        }
    }
}

.upload-demo {
    width: 100%;

    .el-upload {
        width: 100%;
    }

    .el-upload-dragger {
        width: 100%;
        height: 140px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        border: 2px dashed #d9d9d9;
        transition: all 0.3s;

        &:hover {
            border-color: #409eff;
            background-color: #f5f7fa;
        }
    }

    .el-icon--upload {
        font-size: 28px;
        color: #409eff;
        margin-bottom: 12px;
    }

    .el-upload__text {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
    }
}
</style>
