<!--
 * @Author: hkh <EMAIL>
 * @Date: 2025-03-17 13:22:45
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-04-02 16:35:09
 * @FilePath: \evacuation-simulation-backend\src\views\yljl\bar.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <ECharts :options="options" class="chart" />
</template>

<script setup>
import { computed, defineProps } from "vue";
const props = defineProps({
    data: {
        type: Object,
        default: () => []
    },
    allNum: {
        type: Number,
        default: 0 
    }
});
const options = computed(() => {
    return {
        grid: {
            top: "30",
            left: 5,
            // right: '30%',
            bottom: "5",
            // containLabel: true
        },
        // title: {text: '供应商产品质量'},
        tooltip: { show: true, trigger: "item" },
        legend: {
            right: 0,
            icon: "circle",
            itemWidth: 12,
            data: ["不合格"],
            show: false
        },
        xAxis: {
            // data: [],
            // type: 'value',
            show: false,
        },
        yAxis: [{   
            type: "category",
            show: false,
            inverse: true,
            data: ["不合格"],
            axisTick: {
                show: false,
            },
        },{
            type: 'category',
            gridIndex: 0,
            // min: min,
            // max: 6,
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
             axisLabel: {
                show: false
            },
        }],
        series: [
            {
                type: "bar",
                name: "不合格",
                data: [(props.data.personNum / props.data.allNum * 100) || 0],
                stack: "柱子",
                // barWidth: '20%',
                barMaxWidth: "10",
                color: "#2993FD",
                itemStyle: {
                    barBorderRadius: [8, 0, 0, 8],
                },
                label: {
                    normal: {
                        show: true,
                        position: [0,-30],
                        formatter: function (obj) {
                            return props.data.exitName;
                        },
                    },
                },
            },
            {
                name: '背景',
                type: 'bar',
                barWidth: '10',
                xAxisIndex: 0,
                yAxisIndex: 1,
                barGap: '-100%',
                data: [100],
                itemStyle: {
                    normal: {
                        color: 'rgba(60,115,255,0.1)',
                        barBorderRadius: [0, 8, 8, 0]
                    },
                },
                label: {
                    normal: {
                        show: true,
                        position: ['70%',-30],
                        formatter: function (obj) {
                            return props.data.personNum + '人';
                        },
                    },
                },
                tooltip: {
                    show: false
                },
                zlevel: 9
            },
        ],
    };
});
</script>

<style scoped lang="scss">
.chart {
    height: 100%;
}
</style>